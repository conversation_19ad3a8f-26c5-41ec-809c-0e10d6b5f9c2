{"transactions": [{"hash": "0xf333beeb9a0bab3877682b3b132c47bbc154ab3c259b92d9d66b79798e1cd960", "transactionType": "CALL", "contractName": "VestPresale", "contractAddress": "0xdf75e9f2f9a8bcdf5aeca65df6636111bea6bed5", "function": "updateTgeTimestamp(uint256)", "arguments": ["1739275200"], "transaction": {"from": "0xe9fd675c27a079fb4ad242f5099bb21be0b944db", "to": "0xdf75e9f2f9a8bcdf5aeca65df6636111bea6bed5", "gas": "0xae71", "value": "0x0", "input": "0xde7af66d0000000000000000000000000000000000000000000000000000000067ab3bc0", "nonce": "0x35", "chainId": "0x38"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0x9d0112", "logs": [{"address": "0xdf75e9f2f9a8bcdf5aeca65df6636111bea6bed5", "topics": ["0x6de97eb9b2c91c4ed87da2828a7611ff87b192b8ca8a23833fa9fa4347eb3971"], "data": "0x0000000000000000000000000000000000000000000000000000000067ab3bc0", "blockHash": "0x3d79503ccc00719fcf35529d20e38eea3a695f8f9ac507777030281ff3a1ff7a", "blockNumber": "0x2be255a", "transactionHash": "0xf333beeb9a0bab3877682b3b132c47bbc154ab3c259b92d9d66b79798e1cd960", "transactionIndex": "0x65", "logIndex": "0xd3", "removed": false}], "logsBloom": "0x00000000000000000000000000000000000000000020000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000", "type": "0x0", "transactionHash": "0xf333beeb9a0bab3877682b3b132c47bbc154ab3c259b92d9d66b79798e1cd960", "transactionIndex": "0x65", "blockHash": "0x3d79503ccc00719fcf35529d20e38eea3a695f8f9ac507777030281ff3a1ff7a", "blockNumber": "0x2be255a", "gasUsed": "0x7e4c", "effectiveGasPrice": "0x3b9aca00", "from": "0xe9fd675c27a079fb4ad242f5099bb21be0b944db", "to": "0xdf75e9f2f9a8bcdf5aeca65df6636111bea6bed5", "contractAddress": null}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1737628801, "chain": 56, "commit": "21f1b88e"}