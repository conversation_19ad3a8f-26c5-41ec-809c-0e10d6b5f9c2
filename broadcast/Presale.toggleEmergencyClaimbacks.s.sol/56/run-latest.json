{"transactions": [{"hash": "0xaacc7fd9325f8b328d7e657f47182b4254daea69885c7541e47b4fced27a8284", "transactionType": "CALL", "contractName": null, "contractAddress": "0x93ce9efdb9547cf4c28133cbd35c0608d224a000", "function": "toggleEmergencyClaimbacks()", "arguments": [], "transaction": {"from": "0xe9fd675c27a079fb4ad242f5099bb21be0b944db", "to": "0x93ce9efdb9547cf4c28133cbd35c0608d224a000", "gas": "0xa987", "value": "0x0", "input": "0x76652a74", "nonce": "0x2b", "chainId": "0x38"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0xdcc22c", "logs": [{"address": "0x93ce9efdb9547cf4c28133cbd35c0608d224a000", "topics": ["0x0d3cde27df7c5357c3de046692cc0f9a918a5c79af5d6c85907c115c43dcb0b6"], "data": "0x0000000000000000000000000000000000000000000000000000000000000002", "blockHash": "0x21bdf8d0e24a34bd00eec3f0f6afa984613d6936eddc0e3165af1d398ffd2e21", "blockNumber": "0x2b0a13c", "transactionHash": "0xaacc7fd9325f8b328d7e657f47182b4254daea69885c7541e47b4fced27a8284", "transactionIndex": "0x81", "logIndex": "0x12b", "removed": false}], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000040000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000800000000000000004000000000000000000000000000000", "type": "0x0", "transactionHash": "0xaacc7fd9325f8b328d7e657f47182b4254daea69885c7541e47b4fced27a8284", "transactionIndex": "0x81", "blockHash": "0x21bdf8d0e24a34bd00eec3f0f6afa984613d6936eddc0e3165af1d398ffd2e21", "blockNumber": "0x2b0a13c", "gasUsed": "0x73eb", "effectiveGasPrice": "0x3b9aca00", "from": "0xe9fd675c27a079fb4ad242f5099bb21be0b944db", "to": "0x93ce9efdb9547cf4c28133cbd35c0608d224a000", "contractAddress": null}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1734971180, "chain": 56, "commit": "c534e20d"}