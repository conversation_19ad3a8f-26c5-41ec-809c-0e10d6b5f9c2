{"name": "vest-evm", "version": "1.0.0", "type": "module", "description": "**Foundry is a blazing fast, portable and modular toolkit for Ethereum application development written in Rust.**", "directories": {"lib": "lib", "test": "test"}, "scripts": {"update-remappings": "bun run ./node-scripts/update-remappings.ts", "outline-tests": "bun run ./node-scripts/outline-tests.ts", "test": "jest scripts", "tree": "bun run ./node-scripts/tree.ts", "postinstall": "patch-package"}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@commander-js/extra-typings": "11.1.0", "@delegatecall/utils": "file:./lib/delegatecall/", "@openzeppelin/contracts": "5.0.1", "@solidity-parser/parser": "0.18.0", "change-case": "5.4.2", "ethers": "5.7.2", "glob": "10.3.10", "patch-package": "8.0.0", "solhint": "4.1.1", "tiny-invariant": "1.3.1", "to-vfile": "8.0.0", "unified": "11.0.4", "vfile-rename": "3.0.0", "yaml": "2.3.4", "zod": "3.22.4"}, "devDependencies": {"@types/bun": "1.1.14", "@types/fs-extra": "11.0.4", "@types/jest": "29.5.11", "@types/node": "20.11.5", "bun": "1.1.38", "fs-extra": "11.2.0", "jest": "29.7.0", "ts-jest": "29.1.1", "typescript": "5.3.3", "vfile": "6.0.1"}}