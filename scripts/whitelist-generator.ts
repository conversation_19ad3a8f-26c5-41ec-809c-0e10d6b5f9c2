#!/usr/bin/env bun
/**
 * Whitelist Merkle Tree Generator for VestPresale
 * 
 * This script generates Merkle trees and proofs compatible with the VestPresale contract.
 * Each leaf contains an address and complete membership attributes for sophisticated
 * whitelist management with different terms per participant.
 */

import { ethers } from 'ethers';
import { keccak256 } from 'ethers/lib/utils';

// Type definitions matching the Solidity structs
interface MembershipAttributes {
  price: string;              // Price per token in wei (as string for BigNumber compatibility)
  allocation: string;         // Maximum tokens user can purchase (as string)
  claimbackPeriod: number;    // Period for refunds (in seconds)
  tgeNumerator: number;       // TGE release percentage numerator
  tgeDenominator: number;     // TGE release percentage denominator
  cliffDuration: number;      // Cliff period duration (in seconds)
  cliffNumerator: number;     // Cliff release percentage numerator
  cliffDenominator: number;   // Cliff release percentage denominator
  vestingPeriodCount: number; // Number of vesting periods
  vestingPeriodDuration: number; // Duration of each vesting period (in seconds)
  tradeable: number;          // Whether tokens are tradeable (0=false, 1=true, 2=unknown)
}

interface WhitelistEntry {
  address: string;
  attributes: MembershipAttributes;
}

interface MerkleProof {
  leaf: string;
  proof: string[];
}

/**
 * Simple Merkle Tree implementation compatible with VestPresale
 * Based on the existing Murky library patterns
 */
class WhitelistMerkleTree {
  private leaves: string[];
  private layers: string[][];

  constructor(entries: WhitelistEntry[]) {
    // Generate leaves from whitelist entries
    this.leaves = entries.map(entry => this.generateLeaf(entry));

    // Build the tree layers
    this.layers = this.buildTree(this.leaves);
  }

  /**
   * Generate a leaf hash compatible with VestPresale._requireCallerIsWhitelisted
   */
  private generateLeaf(entry: WhitelistEntry): string {
    const { address, attributes } = entry;

    // Encode exactly as done in VestPresale._requireCallerIsWhitelisted
    const encoded = ethers.utils.defaultAbiCoder.encode(
      [
        'address',
        'uint256',
        'uint256',
        'uint256',
        'uint32',
        'uint32',
        'uint32',
        'uint32',
        'uint32',
        'uint32',
        'uint32',
        'uint8'
      ],
      [
        address,
        attributes.price,
        attributes.allocation,
        attributes.claimbackPeriod,
        attributes.tgeNumerator,
        attributes.tgeDenominator,
        attributes.cliffDuration,
        attributes.cliffNumerator,
        attributes.cliffDenominator,
        attributes.vestingPeriodCount,
        attributes.vestingPeriodDuration,
        attributes.tradeable
      ]
    );

    return keccak256(encoded);
  }

  /**
   * Build Merkle tree layers using the same algorithm as Murky
   */
  private buildTree(leaves: string[]): string[][] {
    if (leaves.length === 0) {
      throw new Error('Cannot build tree with no leaves');
    }

    const layers: string[][] = [leaves];

    while (layers[layers.length - 1].length > 1) {
      const currentLayer = layers[layers.length - 1];
      const nextLayer: string[] = [];

      for (let i = 0; i < currentLayer.length; i += 2) {
        const left = currentLayer[i];
        const right = i + 1 < currentLayer.length ? currentLayer[i + 1] : ethers.constants.HashZero;

        // Sort and hash (same as Murky's hashLeafPairs)
        const sortedPair = left < right ? [left, right] : [right, left];
        const combined = ethers.utils.concat(sortedPair);
        nextLayer.push(keccak256(combined));
      }

      layers.push(nextLayer);
    }

    return layers;
  }

  /**
   * Get the Merkle root
   */
  getRoot(): string {
    return this.layers[this.layers.length - 1][0];
  }

  /**
   * Generate proof for a specific whitelist entry
   */
  getProof(entry: WhitelistEntry): MerkleProof {
    const leaf = this.generateLeaf(entry);
    const leafIndex = this.leaves.indexOf(leaf);

    if (leafIndex === -1) {
      throw new Error('Entry not found in tree');
    }

    const proof: string[] = [];
    let currentIndex = leafIndex;

    // Generate proof by collecting sibling hashes at each level
    for (let level = 0; level < this.layers.length - 1; level++) {
      const currentLayer = this.layers[level];
      const isRightNode = currentIndex % 2 === 1;
      const siblingIndex = isRightNode ? currentIndex - 1 : currentIndex + 1;

      if (siblingIndex < currentLayer.length) {
        proof.push(currentLayer[siblingIndex]);
      } else {
        proof.push(ethers.constants.HashZero);
      }

      currentIndex = Math.floor(currentIndex / 2);
    }

    return { leaf, proof };
  }

  /**
   * Verify a proof (for testing purposes)
   */
  verifyProof(proof: string[], root: string, leaf: string): boolean {
    let computedHash = leaf;

    for (const proofElement of proof) {
      const sortedPair = computedHash < proofElement
        ? [computedHash, proofElement]
        : [proofElement, computedHash];
      const combined = ethers.utils.concat(sortedPair);
      computedHash = keccak256(combined);
    }

    return computedHash === root;
  }
}

// Example usage and utility functions
export { WhitelistMerkleTree, type WhitelistEntry, type MembershipAttributes, type MerkleProof };

/**
 * Generate proof for a user to call the buy function
 */
function generateBuyProof(
  tree: WhitelistMerkleTree,
  userAddress: string,
  attributes: MembershipAttributes
): { proof: string[], leaf: string } {
  const entry: WhitelistEntry = { address: userAddress, attributes };
  return tree.getProof(entry);
}

/**
 * Validate that user can call buy function with given parameters
 */
function validateBuyParameters(
  userAddress: string,
  attributes: MembershipAttributes,
  amountA: string,
  proof: string[],
  whitelistRoot: string
): { isValid: boolean, errors: string[] } {
  const errors: string[] = [];

  // Check allocation limit
  const allocation = ethers.BigNumber.from(attributes.allocation);
  const requestedAmount = ethers.BigNumber.from(amountA);

  if (requestedAmount.gt(allocation)) {
    errors.push(`Requested amount (${ethers.utils.formatEther(requestedAmount)}) exceeds allocation (${ethers.utils.formatEther(allocation)})`);
  }

  // Validate proof
  const tree = new WhitelistMerkleTree([{ address: userAddress, attributes }]);
  const { leaf } = tree.getProof({ address: userAddress, attributes });

  if (!tree.verifyProof(proof, whitelistRoot, leaf)) {
    errors.push("Invalid Merkle proof");
  }

  // Validate attributes
  if (attributes.tgeDenominator === 0) {
    errors.push("TGE denominator cannot be zero");
  }

  if (attributes.cliffDenominator === 0) {
    errors.push("Cliff denominator cannot be zero");
  }

  if (attributes.tgeNumerator > attributes.tgeDenominator) {
    errors.push("TGE percentage cannot exceed 100%");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// CLI functionality when run directly
if (import.meta.main) {
  // TGLP whitelist data - matching the exact attributes from Presale.buy.s.sol
  const tglpWhitelist: WhitelistEntry[] = [
    {
      address: "******************************************", // Current user address
      attributes: {
        price: "350000", // 0.35 USDC (6 decimals)
        allocation: "1027362870000000000000", // ~1,027.36 tokens (18 decimals)
        claimbackPeriod: 86400, // 24 hours claimback period
        tgeNumerator: 25, // 25% unlock at TGE
        tgeDenominator: 100,
        cliffDuration: 0, // No cliff
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 240, // 8 months vesting (240 daily periods)
        vestingPeriodDuration: 86400, // Daily vesting (86400 seconds)
        tradeable: 1 // Boolean.FALSE (which is 1 in the Boolean library)
      }
    }
  ];

  console.log("🌳 Generating Merkle Tree for TGLP Whitelist");
  console.log("================================================");

  const tree = new WhitelistMerkleTree(tglpWhitelist);
  const root = tree.getRoot();

  console.log(`\n📋 Whitelist Root: ${root}`);
  console.log(`\n👥 Whitelisted Addresses: ${tglpWhitelist.length}`);

  // Generate proofs for each entry
  console.log("\n🔐 Generated Proofs:");
  tglpWhitelist.forEach((entry, index) => {
    const { leaf, proof } = tree.getProof(entry);
    console.log(`\n${index + 1}. ${entry.address}`);
    console.log(`   Leaf: ${leaf}`);
    console.log(`   Proof: [${proof.map(p => `"${p}"`).join(', ')}]`);

    // Verify the proof
    const isValid = tree.verifyProof(proof, root, leaf);
    console.log(`   ✅ Proof Valid: ${isValid}`);

    // Show buy function call example
    console.log(`   💰 Max Purchase: ${(parseInt(entry.attributes.allocation) / 1e18).toFixed(6)} tokens`);
    console.log(`   💵 Price: ${(parseInt(entry.attributes.price) / 1e6).toFixed(6)} USDC per token`);
  });

  // Demonstrate buy validation
  console.log("\n🔍 Buy Function Validation Example:");
  const userAddress = tglpWhitelist[0].address;
  const userAttributes = tglpWhitelist[0].attributes;
  const { proof } = tree.getProof(tglpWhitelist[0]);
  const requestedAmount = "2857142857142857142"; // ~2.857 tokens to buy (matching script)

  const validation = validateBuyParameters(userAddress, userAttributes, requestedAmount, proof, root);
  console.log(`User: ${userAddress}`);
  console.log(`Requested: ${(parseInt(requestedAmount) / 1e18).toFixed(6)} tokens`);
  console.log(`Valid: ${validation.isValid}`);
  if (!validation.isValid) {
    validation.errors.forEach(error => console.log(`  ❌ ${error}`));
  }

  console.log("\n🚀 Ready for deployment! Use the root hash with updateRoundWhitelist()");
  console.log("\n📋 Copy this proof array to your Solidity script:");
  console.log(`bytes32[] internal proof = [${tglpWhitelist[0] ? tree.getProof(tglpWhitelist[0]).proof.map(p => `bytes32(${p})`).join(', ') : ''}];`);

  // Debug encoding
  console.log("\n🔍 Debug Information:");
  console.log("Expected leaf hash (from TypeScript):", tree.getProof(tglpWhitelist[0]).leaf);
  console.log("Address:", tglpWhitelist[0].address);
  console.log("Attributes:", JSON.stringify(tglpWhitelist[0].attributes, null, 2));
}
