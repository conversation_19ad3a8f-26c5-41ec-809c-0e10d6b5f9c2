#!/usr/bin/env bun
/**
 * Deploy Whitelist Script
 * 
 * Complete workflow for generating and deploying whitelists to VestPresale contracts.
 * This script demonstrates the end-to-end process from CSV data to on-chain deployment.
 */

import { ethers } from 'ethers';
import { WhitelistBuilder, TIER_CONFIGS } from './participants-helper';
import fs from 'fs';
import path from 'path';

// Configuration
interface DeployConfig {
  rpcUrl: string;
  privateKey: string;
  presaleAddress: string;
  roundId: number;
  proofsUri?: string;
}

interface CSVParticipant {
  address: string;
  tier: 'SEED' | 'PRIVATE' | 'PUBLIC' | 'TEAM';
  customPrice?: string;
  customAllocation?: string;
}

/**
 * Load participants from CSV file
 */
function loadParticipantsFromCSV(csvPath: string): CSVParticipant[] {
  if (!fs.existsSync(csvPath)) {
    throw new Error(`CSV file not found: ${csvPath}`);
  }

  const csvContent = fs.readFileSync(csvPath, 'utf-8');
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',').map(h => h.trim());
  
  const participants: CSVParticipant[] = [];
  
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim());
    const participant: any = {};
    
    headers.forEach((header, index) => {
      participant[header] = values[index];
    });
    
    // Validate required fields
    if (!participant.address || !participant.tier) {
      console.warn(`Skipping invalid row ${i}: missing address or tier`);
      continue;
    }
    
    // Validate address format
    try {
      ethers.utils.getAddress(participant.address);
    } catch {
      console.warn(`Skipping invalid address at row ${i}: ${participant.address}`);
      continue;
    }
    
    // Validate tier
    if (!['SEED', 'PRIVATE', 'PUBLIC', 'TEAM'].includes(participant.tier)) {
      console.warn(`Skipping invalid tier at row ${i}: ${participant.tier}`);
      continue;
    }
    
    participants.push(participant as CSVParticipant);
  }
  
  return participants;
}

/**
 * Generate whitelist from CSV data
 */
function generateWhitelistFromCSV(csvPath: string) {
  console.log(`📄 Loading participants from ${csvPath}...`);
  
  const csvParticipants = loadParticipantsFromCSV(csvPath);
  console.log(`✅ Loaded ${csvParticipants.length} participants`);
  
  const builder = new WhitelistBuilder();
  
  // Add participants with custom attributes if specified
  csvParticipants.forEach(({ address, tier, customPrice, customAllocation }) => {
    const customAttributes: any = {};
    
    if (customPrice) {
      customAttributes.price = ethers.utils.parseEther(customPrice).toString();
    }
    
    if (customAllocation) {
      customAttributes.allocation = ethers.utils.parseEther(customAllocation).toString();
    }
    
    builder.addParticipant(address, tier, Object.keys(customAttributes).length > 0 ? customAttributes : undefined);
  });
  
  // Validate and build
  const validation = builder.validate();
  if (!validation.isValid) {
    console.error("❌ Validation failed:");
    validation.errors.forEach(error => console.error(`  - ${error}`));
    throw new Error("Whitelist validation failed");
  }
  
  return builder.build();
}

/**
 * Save whitelist data to files
 */
function saveWhitelistData(whitelist: any, outputDir: string) {
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Save root hash
  fs.writeFileSync(
    path.join(outputDir, 'whitelist-root.txt'),
    whitelist.root
  );
  
  // Save proofs as JSON
  const proofsData = whitelist.proofs.reduce((acc: any, p: any) => {
    acc[p.address] = {
      leaf: p.leaf,
      proof: p.proof,
      attributes: p.attributes
    };
    return acc;
  }, {});
  
  fs.writeFileSync(
    path.join(outputDir, 'whitelist-proofs.json'),
    JSON.stringify(proofsData, null, 2)
  );
  
  // Save Foundry-compatible format
  const foundryData = {
    whitelistRoot: whitelist.root,
    proofs: whitelist.exportForFoundry().proofs
  };
  
  fs.writeFileSync(
    path.join(outputDir, 'foundry-data.json'),
    JSON.stringify(foundryData, null, 2)
  );
  
  console.log(`💾 Whitelist data saved to ${outputDir}/`);
}

/**
 * Deploy whitelist to contract
 */
async function deployWhitelist(config: DeployConfig, whitelistRoot: string) {
  console.log("🚀 Deploying whitelist to contract...");
  
  const provider = new ethers.providers.JsonRpcProvider(config.rpcUrl);
  const wallet = new ethers.Wallet(config.privateKey, provider);
  
  // VestPresale ABI (minimal for updateRoundWhitelist)
  const presaleABI = [
    "function updateRoundWhitelist(uint256 roundId, bytes32 whitelistRoot, string memory proofsUri)"
  ];
  
  const presale = new ethers.Contract(config.presaleAddress, presaleABI, wallet);
  
  try {
    const tx = await presale.updateRoundWhitelist(
      config.roundId,
      whitelistRoot,
      config.proofsUri || ""
    );
    
    console.log(`📝 Transaction sent: ${tx.hash}`);
    console.log("⏳ Waiting for confirmation...");
    
    const receipt = await tx.wait();
    console.log(`✅ Whitelist deployed! Block: ${receipt.blockNumber}`);
    console.log(`   Round ID: ${config.roundId}`);
    console.log(`   Whitelist Root: ${whitelistRoot}`);
    
    return receipt;
  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log("🌳 Whitelist Generator and Deployer");
    console.log("===================================");
    console.log("");
    console.log("Usage:");
    console.log("  bun run deploy-whitelist.ts generate <csv-file> [output-dir]");
    console.log("  bun run deploy-whitelist.ts deploy <config-file> <whitelist-root>");
    console.log("");
    console.log("Examples:");
    console.log("  bun run deploy-whitelist.ts generate participants.csv ./output");
    console.log("  bun run deploy-whitelist.ts deploy config.json 0x1234...");
    console.log("");
    console.log("CSV Format:");
    console.log("  address,tier,customPrice,customAllocation");
    console.log("  0x1234...,SEED,,");
    console.log("  0x5678...,PRIVATE,0.09,2000");
    return;
  }
  
  const command = args[0];
  
  if (command === 'generate') {
    const csvFile = args[1];
    const outputDir = args[2] || './whitelist-output';
    
    if (!csvFile) {
      console.error("❌ CSV file path required");
      process.exit(1);
    }
    
    try {
      const whitelist = generateWhitelistFromCSV(csvFile);
      saveWhitelistData(whitelist, outputDir);
      
      const summary = new WhitelistBuilder().addFromData(
        loadParticipantsFromCSV(csvFile).map(p => ({
          address: p.address,
          tier: p.tier as any,
          customAttributes: {}
        }))
      ).getSummary();
      
      console.log("\n📊 Summary:");
      console.log(`  Participants: ${summary.totalParticipants}`);
      console.log(`  Total Allocation: ${summary.totalAllocation} tokens`);
      console.log(`  Whitelist Root: ${whitelist.root}`);
      
    } catch (error) {
      console.error("❌ Generation failed:", error);
      process.exit(1);
    }
    
  } else if (command === 'deploy') {
    const configFile = args[1];
    const whitelistRoot = args[2];
    
    if (!configFile || !whitelistRoot) {
      console.error("❌ Config file and whitelist root required");
      process.exit(1);
    }
    
    try {
      const config: DeployConfig = JSON.parse(fs.readFileSync(configFile, 'utf-8'));
      await deployWhitelist(config, whitelistRoot);
      
    } catch (error) {
      console.error("❌ Deployment failed:", error);
      process.exit(1);
    }
    
  } else {
    console.error(`❌ Unknown command: ${command}`);
    process.exit(1);
  }
}

// Example CSV content for documentation
const EXAMPLE_CSV = `address,tier,customPrice,customAllocation
******************************************,SEED,,
******************************************,SEED,,
******************************************,PRIVATE,,
******************************************,PRIVATE,0.09,2000
******************************************,PUBLIC,,
******************************************,TEAM,,`;

const EXAMPLE_CONFIG = {
  rpcUrl: "https://rpc.ankr.com/eth",
  privateKey: "0x...", // Your private key
  presaleAddress: "0x...", // VestPresale contract address
  roundId: 1,
  proofsUri: "https://your-api.com/proofs" // Optional
};

// Export for use as module
export { generateWhitelistFromCSV, deployWhitelist, saveWhitelistData };

// Run if called directly
if (import.meta.main) {
  main().catch(console.error);
}
