#!/usr/bin/env bun
/**
 * Participants Helper - Advanced Whitelist Management
 * 
 * This script provides utilities for working with the existing Participants.sol
 * library and generating complex whitelists with multiple tiers and configurations.
 */

import { ethers } from 'ethers';
import { WhitelistMerkleTree, type WhitelistEntry, type MembershipAttributes } from './whitelist-generator';

// Predefined tier configurations for common use cases
const TIER_CONFIGS = {
  SEED: {
    price: ethers.utils.parseEther("0.05").toString(),
    allocation: ethers.utils.parseEther("10000").toString(),
    claimbackPeriod: 7 * 24 * 60 * 60, // 7 days
    tgeNumerator: 15,
    tgeDenominator: 100,
    cliffDuration: 60 * 24 * 60 * 60, // 60 days
    cliffNumerator: 10,
    cliffDenominator: 100,
    vestingPeriodCount: 18,
    vestingPeriodDuration: 30 * 24 * 60 * 60, // 30 days
    tradeable: 0 // Not tradeable initially
  },
  
  PRIVATE: {
    price: ethers.utils.parseEther("0.08").toString(),
    allocation: ethers.utils.parseEther("5000").toString(),
    claimbackPeriod: 5 * 24 * 60 * 60, // 5 days
    tgeNumerator: 20,
    tgeDenominator: 100,
    cliffDuration: 30 * 24 * 60 * 60, // 30 days
    cliffNumerator: 15,
    cliffDenominator: 100,
    vestingPeriodCount: 12,
    vestingPeriodDuration: 30 * 24 * 60 * 60,
    tradeable: 1
  },
  
  PUBLIC: {
    price: ethers.utils.parseEther("0.12").toString(),
    allocation: ethers.utils.parseEther("1000").toString(),
    claimbackPeriod: 24 * 60 * 60, // 24 hours
    tgeNumerator: 25,
    tgeDenominator: 100,
    cliffDuration: 0, // No cliff
    cliffNumerator: 0,
    cliffDenominator: 1,
    vestingPeriodCount: 6,
    vestingPeriodDuration: 30 * 24 * 60 * 60,
    tradeable: 1
  },
  
  TEAM: {
    price: "0", // Free allocation
    allocation: ethers.utils.parseEther("50000").toString(),
    claimbackPeriod: 0, // No claimback for team
    tgeNumerator: 0, // No TGE release
    tgeDenominator: 100,
    cliffDuration: 365 * 24 * 60 * 60, // 1 year cliff
    cliffNumerator: 0,
    cliffDenominator: 1,
    vestingPeriodCount: 36,
    vestingPeriodDuration: 30 * 24 * 60 * 60,
    tradeable: 0 // Not tradeable
  }
} as const;

type TierType = keyof typeof TIER_CONFIGS;

interface ParticipantTier {
  address: string;
  tier: TierType;
  customAttributes?: Partial<MembershipAttributes>;
}

/**
 * Advanced whitelist builder with tier support
 */
class WhitelistBuilder {
  private participants: WhitelistEntry[] = [];

  /**
   * Add a participant with a predefined tier
   */
  addParticipant(address: string, tier: TierType, customAttributes?: Partial<MembershipAttributes>): this {
    const baseAttributes = TIER_CONFIGS[tier];
    const attributes = { ...baseAttributes, ...customAttributes };
    
    this.participants.push({
      address: ethers.utils.getAddress(address), // Normalize address
      attributes
    });
    
    return this;
  }

  /**
   * Add multiple participants with the same tier
   */
  addParticipants(addresses: string[], tier: TierType, customAttributes?: Partial<MembershipAttributes>): this {
    addresses.forEach(address => {
      this.addParticipant(address, tier, customAttributes);
    });
    return this;
  }

  /**
   * Add participants from a CSV-like structure
   */
  addFromData(participants: ParticipantTier[]): this {
    participants.forEach(({ address, tier, customAttributes }) => {
      this.addParticipant(address, tier, customAttributes);
    });
    return this;
  }

  /**
   * Build the Merkle tree and return comprehensive data
   */
  build() {
    if (this.participants.length === 0) {
      throw new Error('No participants added to whitelist');
    }

    const tree = new WhitelistMerkleTree(this.participants);
    const root = tree.getRoot();
    
    // Generate proofs for all participants
    const proofs = this.participants.map(participant => ({
      address: participant.address,
      attributes: participant.attributes,
      ...tree.getProof(participant)
    }));

    return {
      root,
      participants: this.participants,
      proofs,
      tree,
      // Utility methods
      getProofForAddress: (address: string) => {
        const participant = this.participants.find(p => 
          p.address.toLowerCase() === address.toLowerCase()
        );
        if (!participant) {
          throw new Error(`Address ${address} not found in whitelist`);
        }
        return tree.getProof(participant);
      },
      // Export for Foundry scripts
      exportForFoundry: () => ({
        whitelistRoot: root,
        proofs: proofs.reduce((acc, p) => {
          acc[p.address] = p.proof;
          return acc;
        }, {} as Record<string, string[]>)
      })
    };
  }

  /**
   * Validate all participants before building
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for duplicate addresses
    const addresses = this.participants.map(p => p.address.toLowerCase());
    const duplicates = addresses.filter((addr, index) => addresses.indexOf(addr) !== index);
    if (duplicates.length > 0) {
      errors.push(`Duplicate addresses found: ${duplicates.join(', ')}`);
    }

    // Validate each participant's attributes
    this.participants.forEach((participant, index) => {
      const { attributes } = participant;
      
      // Check denominators are not zero
      if (attributes.tgeDenominator === 0) {
        errors.push(`Participant ${index}: tgeDenominator cannot be zero`);
      }
      if (attributes.cliffDenominator === 0) {
        errors.push(`Participant ${index}: cliffDenominator cannot be zero`);
      }
      
      // Check TGE percentage is valid
      if (attributes.tgeNumerator > attributes.tgeDenominator) {
        errors.push(`Participant ${index}: TGE percentage cannot exceed 100%`);
      }
      
      // Check cliff percentage is valid
      if (attributes.cliffNumerator > attributes.cliffDenominator) {
        errors.push(`Participant ${index}: Cliff percentage cannot exceed 100%`);
      }
      
      // Check vesting periods
      if (attributes.vestingPeriodCount === 0) {
        errors.push(`Participant ${index}: vestingPeriodCount cannot be zero`);
      }
      if (attributes.vestingPeriodDuration === 0) {
        errors.push(`Participant ${index}: vestingPeriodDuration cannot be zero`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get summary statistics
   */
  getSummary() {
    const tierCounts = this.participants.reduce((acc, p) => {
      // Determine tier by comparing attributes
      const tier = Object.entries(TIER_CONFIGS).find(([_, config]) => 
        config.price === p.attributes.price &&
        config.allocation === p.attributes.allocation
      )?.[0] || 'CUSTOM';
      
      acc[tier] = (acc[tier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalAllocation = this.participants.reduce((sum, p) => 
      sum.add(ethers.BigNumber.from(p.attributes.allocation)), 
      ethers.BigNumber.from(0)
    );

    return {
      totalParticipants: this.participants.length,
      tierBreakdown: tierCounts,
      totalAllocation: ethers.utils.formatEther(totalAllocation),
      averagePrice: this.participants.length > 0 
        ? ethers.utils.formatEther(
            this.participants.reduce((sum, p) => 
              sum.add(ethers.BigNumber.from(p.attributes.price)), 
              ethers.BigNumber.from(0)
            ).div(this.participants.length)
          )
        : '0'
    };
  }
}

// Export utilities
export { WhitelistBuilder, TIER_CONFIGS, type TierType, type ParticipantTier };

// CLI functionality when run directly
if (import.meta.main) {
  console.log("🏗️  Advanced Whitelist Builder Example");
  console.log("=====================================");

  const builder = new WhitelistBuilder();

  // Add different tiers of participants
  builder
    .addParticipants([
      "******************************************",
      "******************************************"
    ], "SEED")
    .addParticipants([
      "******************************************",
      "******************************************",
      "******************************************"
    ], "PRIVATE")
    .addParticipants([
      "******************************************",
      "******************************************"
    ], "PUBLIC")
    .addParticipant("0x8901234567890123456789012345678901234567", "TEAM");

  // Validate before building
  const validation = builder.validate();
  if (!validation.isValid) {
    console.error("❌ Validation failed:");
    validation.errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }

  // Build the whitelist
  const whitelist = builder.build();
  
  // Show summary
  const summary = builder.getSummary();
  console.log("\n📊 Whitelist Summary:");
  console.log(`  Total Participants: ${summary.totalParticipants}`);
  console.log(`  Total Allocation: ${summary.totalAllocation} tokens`);
  console.log(`  Average Price: ${summary.averagePrice} ETH`);
  console.log("\n🎯 Tier Breakdown:");
  Object.entries(summary.tierBreakdown).forEach(([tier, count]) => {
    console.log(`  ${tier}: ${count} participants`);
  });

  console.log(`\n🌳 Merkle Root: ${whitelist.root}`);
  console.log("\n✅ Whitelist generated successfully!");
  console.log("\n💡 Use this root with the updateRoundWhitelist function:");
  console.log(`presale.updateRoundWhitelist(roundId, "${whitelist.root}", "");`);
}
