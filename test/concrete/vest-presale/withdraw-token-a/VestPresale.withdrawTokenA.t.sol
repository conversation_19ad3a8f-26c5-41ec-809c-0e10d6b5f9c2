// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestTest } from "test/VestTest.sol";
import { Participants } from "test/utils/Participants.sol";

contract Presale_withdrawTokenA is VestTest {
    using Participants for Participants.Collection;

    uint256 internal roundId = 1;

    uint256 sold;
    uint256 liquidityA;
    uint256 membershipId;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());

        (liquidityA,) = presale.roundLiquidityA(roundId);
    }

    function test_GivenTheCallerIsNotBeneficiary() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.withdrawTokenA(roundId, 1 ether);
    }

    modifier givenTheCallerIsBeneficiary() {
        vm.startPrank(beneficiary);
        _;
    }

    function test_GivenNoTokensWereSold() external givenTheCallerIsBeneficiary {
        // it transfers all deposited tokenA to the beneficiary
        presale.withdrawTokenA(roundId, liquidityA);

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        assertEq(liquidityAAfter, 0);
        assertEq(tokenA.balanceOf(beneficiary), liquidityA);
    }

    modifier givenSomeTokensWereSold() {
        membershipId = fixture_buy(roundId, alice);
        sold += participants.get(roundId, alice).allocation;

        fixture_buy(roundId, bob);
        sold += participants.get(roundId, bob).allocation;
        _;
    }

    function test_GivenNoClaimbacksWereMade() external givenSomeTokensWereSold givenTheCallerIsBeneficiary {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // it transfers all deposited tokenA to the beneficiary minus tokens sold minus the success fee
        presale.withdrawTokenA(roundId, liquidityABefore);

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        assertEq(liquidityAAfter, 0);
        assertEq(tokenA.balanceOf(beneficiary), liquidityA - sold);
    }

    function test_GivenClaimbacksWereMade() external givenSomeTokensWereSold givenTheCallerIsBeneficiary {
        vm.stopPrank();

        Participants.Participant memory participant = participants.get(roundId, alice);

        vm.prank(alice);
        membershipId = presale.claimback(membershipId, participant.allocation);

        vm.startPrank(beneficiary);

        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // it transfers all deposited tokenA to the beneficiary minus tokens sold plus tokens claimed back minus the success fee
        presale.withdrawTokenA(roundId, liquidityABefore);

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        assertEq(liquidityAAfter, 0);
        assertEq(tokenA.balanceOf(beneficiary), liquidityA - sold + participant.allocation);
    }
}
