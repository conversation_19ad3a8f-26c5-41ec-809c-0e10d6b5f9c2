// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Presale } from "src/types/Configuration.sol";

import { VestTest } from "test/VestTest.sol";

contract Presale_getFees is VestTest {
    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_ReturnsFees() external {
        // it returns fees
        Presale.Fees memory fees_ = presale.getFees();

        assertEq(fees_.tokenANumerator, fees.tokenANumerator, "it returns tokenANumerator");
        assertEq(fees_.tokenADenominator, fees.tokenADenominator, "it returns tokenADenominator");
        assertEq(fees_.tokenBNumerator, fees.tokenBNumerator, "it returns tokenBNumerator");
        assertEq(fees_.tokenBDenominator, fees.tokenBDenominator, "it returns tokenBDenominator");
    }
}
