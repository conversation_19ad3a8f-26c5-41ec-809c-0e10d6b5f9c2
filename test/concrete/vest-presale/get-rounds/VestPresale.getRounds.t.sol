// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Round, RoundState } from "src/types/Round.sol";

import { VestTest } from "test/VestTest.sol";

contract Presale_getRounds is VestTest {
    Round internal round;

    function setUp() public override {
        super.setUp();

        Round[] memory rounds = new Round[](1);

        round = rounds[0] = Round({
            name: "ROUND I",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: "https://example.com",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: ""
        });

        fixture(rounds);
    }

    function test_ReturnsIdsRoundsAndStates() external {
        // it returns ids rounds and states
        (uint256[] memory ids, Round[] memory rounds, RoundState[] memory states) = presale.getRounds();

        assertEq(ids.length, 1, "returns the ids array");

        assertEq(rounds[0].name, round.name, "first round name equals round.name");
        assertEq(
            rounds[0].startTimestamp, round.startTimestamp, "first round startTimestamp equals round.startTimestamp"
        );
        assertEq(rounds[0].endTimestamp, round.endTimestamp, "first round endTimestamp equals round.endTimestamp");
        assertEq(rounds[0].whitelistRoot, round.whitelistRoot, "first round whitelistRoot equals round.whitelistRoot");
        assertEq(rounds[0].proofsUri, round.proofsUri, "first round proofsUri equals round.proofsUri");
        assertEq(rounds[0].bonusNumerator, round.bonusNumerator, "first round bonusNumerator equals round.bonusNumerator");
        assertEq(rounds[0].bonusDenominator, round.bonusDenominator, "first round bonusDenominator equals round.bonusDenominator");

        assertEq(states.length, 1, "returns the states array");
    }
}
