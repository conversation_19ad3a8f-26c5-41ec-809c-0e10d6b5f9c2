// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Round } from "src/types/Round.sol";
import { VestPresale } from "src/VestPresale.sol";

import { VestTest } from "test/VestTest.sol";

contract Presale_getRound is VestTest {
    Round internal round = Round({
        name: "ROUND I",
        startTimestamp: 1,
        endTimestamp: 2,
        whitelistRoot: bytes32(0),
        proofsUri: "",
        bonusNumerator: 0,
        bonusDenominator: 100,
        attributes: ""
    });

    function setUp() public override {
        super.setUp();

        Round[] memory rounds = new Round[](1);

        rounds[0] = round;

        fixture(rounds);
    }

    function test_GivenRoundDoesNotExist() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, 2));
        presale.getRound(2);
    }

    function test_GivenRoundExists() external {
        // it returns round
        Round memory round_ = presale.getRound(1);

        assertEq(round_.name, round.name, "round_.name equals defined name");
        assertEq(round_.startTimestamp, round.startTimestamp, "round_.startTimestamp equals defined startTimestamp");
        assertEq(round_.endTimestamp, round.endTimestamp, "round_.endTimestamp equals defined endTimestamp");
        assertEq(round_.whitelistRoot, round.whitelistRoot, "round_.whitelistRoot equals defined whitelistRoot");
        assertEq(round_.proofsUri, round.proofsUri, "round_.proofsUri equals defined proofsUri");
        assertEq(round_.bonusNumerator, round.bonusNumerator, "round_.bonusNumerator equals defined bonusNumerator");
        assertEq(round_.bonusDenominator, round.bonusDenominator, "round_.bonusDenominator equals defined bonusDenominator");
    }
}
