// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";
import { IVestMembership } from "src/IVestMembership.sol";

import { Participants } from "test/utils/Participants.sol";
import { VestTest, Configuration } from "test/VestTest.sol";
import { Boolean } from "src/libraries/Boolean.sol";

contract Presale_claimback is VestTest {
    using Participants for Participants.Collection;

    uint256 internal roundId = 1;

    uint256 internal membershipId;

    Participants.Participant internal participant;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_GivenTheCallerDoesNotOwnTheMembership() external {
        membershipId = fixture_buy(roundId, alice);
        participant = participants.get(roundId, alice);

        vm.startPrank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, chuck));
        presale.claimback(membershipId, participant.allocation);
    }

    modifier givenTheCallerOwnsTheMembership(address account, uint256 claimbackPeriod) {
        Participants.Participant memory participant_ = participants.get(roundId, account);

        participants.replace(
            roundId,
            account,
            participant_.price,
            participant_.allocation,
            claimbackPeriod,
            participant_.tgeNumerator,
            participant_.tgeDenominator,
            participant_.cliffDuration,
            participant_.cliffNumerator,
            participant_.cliffDenominator,
            participant_.vestingPeriodCount,
            participant_.vestingPeriodDuration
        );

        vm.startPrank(manager);
        presale.updateRoundWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        uint256 timestamp = block.timestamp;

        membershipId = fixture_buy(roundId, account);
        participant = participants.get(roundId, account);

        vm.warp(timestamp);
        vm.startPrank(account);
        _;
    }

    modifier enableEmergencyClaimbacks() {
        fixture_enableEmergencyClaimbacks();
        _;
    }

    function test_WhenAUserRefundsZeroTokens() external givenTheCallerOwnsTheMembership(alice, 7 days) {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, 0);
    }

    function test_GivenEmergencyClaimbacksAreEnabled()
        external
        enableEmergencyClaimbacks
        givenTheCallerOwnsTheMembership(alice, 0)
    {
        assertEq(participant.claimbackPeriod, 0, "membership claimbackPeriod equals zero");

        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        uint256 liquidityBBefore = presale.liquidityB();
        uint256 claimedBackBefore = presale.claimedBackTokenB();

        vm.expectEmit(true, false, false, true);
        emit VestPresale.ClaimedBack(membershipId, participant.allocation);

        // it refunds
        membershipId = presale.claimback(membershipId, participant.allocation);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;

        // it transfers tokenB to the caller
        assertEq(tokenB.balanceOf(alice), amountB, "it transfers tokenB to the caller");

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        // it increases roundLiquidityA
        assertEq(liquidityAAfter, liquidityABefore + participant.allocation, "it increases roundLiquidityA");

        // it lowers the Membership usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, 0, "usage.max equals zero");

        // it increases claimedBackTokenB
        assertEq(presale.claimedBackTokenB(), claimedBackBefore + amountB, "it increases claimedBackTokenB");

        // it does not change liquidityB
        assertEq(presale.liquidityB(), liquidityBBefore, "it does not change liquidityB");
    }

    function test_GivenEmergencyClaimbacksAreDisabledAndAMembershipClaimbackPeriodAttributeEqualsZero()
        external
        givenTheCallerOwnsTheMembership(alice, 0)
    {
        assertEq(presale.areEmergencyClaimbacksEnabled(), Boolean.FALSE);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }

    modifier givenClaimbackPeriodEqualsZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.claimbackPeriod = 0;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration, composeRounds());
        _;
    }

    function test_GivenTheListingTimestampIsZero()
        external
        givenClaimbackPeriodEqualsZero
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // it emits ClaimedBack event with Membership id and amountA
        vm.expectEmit(true, false, false, true);
        emit VestPresale.ClaimedBack(membershipId, participant.allocation);

        // it claimbacks to prevent griefing
        membershipId = presale.claimback(membershipId, participant.allocation);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;

        // it transfers tokenB to the caller
        assertEq(tokenB.balanceOf(alice), amountB, "it transfers tokenB to the caller");

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        // it increases roundLiquidityA
        assertEq(liquidityAAfter, liquidityABefore + participant.allocation, "it increases roundLiquidityA");

        // it lowers the Membership usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.max, 0, "usage.max equals zero");
    }

    modifier givenTheListingTimestampIsNotZero() {
        vm.prank(manager);
        presale.updateListingTimestamp(vm.unixTime() * 2);

        vm.prank(beneficiary);
        presale.acceptListingTimestamp();
        _;
    }

    function test_GivenBlockTimestampIsLowerThanTgeTimestamp(uint256 timestamp)
        external
        givenClaimbackPeriodEqualsZero
        givenTheListingTimestampIsNotZero
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        vm.assume(timestamp < presale.getTgeTimestamp());

        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        vm.warp(timestamp);

        // it emits ClaimedBack event with Membership id and amountA
        vm.expectEmit(true, false, false, true);
        emit VestPresale.ClaimedBack(membershipId, participant.allocation);

        // it claimbacks to prevent griefing
        membershipId = presale.claimback(membershipId, participant.allocation);

        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;

        // it transfers tokenB to the caller
        assertEq(tokenB.balanceOf(alice), amountB, "it transfers tokenB to the caller");

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        // it increases roundLiquidityA
        assertEq(liquidityAAfter, liquidityABefore + participant.allocation, "it increases roundLiquidityA");

        // it lowers the Membership usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, 0, "usage.max equals zero");
    }

    function test_GivenBlockTimestampIsAfterOrAtTgeTimestampAndBeforeTheListingTimestamp()
        external
        givenClaimbackPeriodEqualsZero
        givenTheListingTimestampIsNotZero
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        vm.warp(presale.getListingTimestamp() + 1);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }

    function test_GivenBlockTimestampIsAfterOrAtListingTimestamp(uint256 timestamp)
        external
        givenClaimbackPeriodEqualsZero
        givenTheListingTimestampIsNotZero
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        vm.assume(timestamp >= presale.getListingTimestamp());

        vm.warp(timestamp);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }

    function test_GivenTheRoundStateIsSale()
        external
        givenClaimbackPeriodEqualsZero
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        uint256 startTimestamp = presale.getRound(roundId).startTimestamp;
        vm.warp(startTimestamp + 1);

        uint256 liquidityBBefore = presale.liquidityB();
        uint256 claimedBackBefore = presale.claimedBackTokenB();

        membershipId = presale.claimback(membershipId, participant.allocation);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;

        // it lowers liquidityB
        assertEq(presale.liquidityB(), liquidityBBefore - amountB, "it lowers liquidityB");

        // it does not change claimedBackTokenB
        assertEq(presale.claimedBackTokenB(), claimedBackBefore, "it does not change claimedBackTokenB");
    }

    function test_GivenTheRoundStateIsNotSale()
        external
        givenClaimbackPeriodEqualsZero
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        uint256 claimedBackBefore = presale.claimedBackTokenB();
        uint256 liquidityBBefore = presale.liquidityB();

        membershipId = presale.claimback(membershipId, participant.allocation);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;

        // it increases claimedBackTokenB
        assertEq(presale.claimedBackTokenB(), claimedBackBefore + amountB, "it increases claimedBackTokenB");

        // it does not change liquidityB
        assertEq(presale.liquidityB(), liquidityBBefore, "it does not change liquidityB");
    }

    modifier givenClaimbackPeriodGreaterThanZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.claimbackPeriod = 7 days;

        fixture(configuration, composeRounds());
        _;
    }

    function test_GivenEmergencyClaimbacksAreDisabledAndBlockTimestampGreaterThanTheSumOfListingTimestampAndPresaleClaimbackPeriod(
    ) external givenClaimbackPeriodGreaterThanZero givenTheCallerOwnsTheMembership(alice, 7 days) {
        assertEq(presale.areEmergencyClaimbacksEnabled(), Boolean.FALSE);

        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod());

        // it reverts because the refunds period passed
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }

    modifier givenBlockTimestampLowerThanOrEqualToTheSumOfListingTimestampAndPresaleClaimbackPeriod() {
        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod() - 1);
        _;
    }

    function test_GivenTheMembershipWasUsedToClaimTokenA()
        external
        givenClaimbackPeriodGreaterThanZero
        givenBlockTimestampLowerThanOrEqualToTheSumOfListingTimestampAndPresaleClaimbackPeriod
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        membershipId = presale.claim(membershipId);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }

    function test_WhenTheCallerRefundsTheirEntireUsage()
        external
        givenClaimbackPeriodGreaterThanZero
        givenBlockTimestampLowerThanOrEqualToTheSumOfListingTimestampAndPresaleClaimbackPeriod
        givenTheCallerOwnsTheMembership(alice, 7 days)
    {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // it emits Refunded event with Membership id and amountA
        vm.expectEmit(true, false, false, true);
        emit VestPresale.ClaimedBack(membershipId, participant.allocation);

        // it refunds
        membershipId = presale.claimback(membershipId, participant.allocation);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;

        // it transfers tokenB to the caller
        assertEq(tokenB.balanceOf(alice), amountB, "it transfers tokenB to the caller");

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);

        // it increases roundLiquidityA
        assertEq(liquidityAAfter, liquidityABefore + participant.allocation, "it increases roundLiquidityA");

        // it lowers the Membership usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, 0, "usage.max equals zero");
    }

    function test_GivenEmergencyClaimbacksAreDisabledAndBlockTimestampGreaterThanTheSumOfListingTimestampAndMembershipClaimbackPeriod(
    )
        external
        givenClaimbackPeriodGreaterThanZero
        givenBlockTimestampLowerThanOrEqualToTheSumOfListingTimestampAndPresaleClaimbackPeriod
        givenTheCallerOwnsTheMembership(alice, 2 days)
    {
        assertEq(presale.areEmergencyClaimbacksEnabled(), Boolean.FALSE);

        // 1711026064836
        // 1711025892036

        // it reverts because the refunds period passed
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }

    function test_GivenEmergencyClaimbacksAreDisabledAndBlockTimestampGreaterThanTheSumOfListingTimestampAndPresaleClaimbackPeriodAndMembershipClaimbackPeriodIsGreaterThanPresaleClaimbackPeriod(
    ) external givenClaimbackPeriodGreaterThanZero givenTheCallerOwnsTheMembership(alice, 14 days) {
        assertEq(presale.areEmergencyClaimbacksEnabled(), Boolean.FALSE);

        vm.warp(presale.getListingTimestamp() + participant.claimbackPeriod - 1);

        // it reverts because the refunds period passed
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimbackNotAllowed.selector, membershipId));
        presale.claimback(membershipId, participant.allocation);
    }
}
