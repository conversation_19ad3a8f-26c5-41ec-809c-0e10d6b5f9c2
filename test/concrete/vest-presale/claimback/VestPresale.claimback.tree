// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_claimback
├── given the caller does not own the Membership
│   └── it reverts
└── given the caller owns the Membership
    ├── when a user refunds zero tokens
    │   └── it reverts
    ├── given emergency claimbacks are disabled and a membership claimbackPeriod attribute equals zero
    │   └── it reverts
    ├── given claimbackPeriod equals zero
    │   ├── given the listingTimestamp is zero
    │   │   ├── it refunds to prevent griefing
    │   │   ├── it emits Refunded event with Membership id and amountA
    │   │   ├── it transfers tokenB to the caller
    │   │   ├── it increases roundLiquidityA
    │   │   └── it lowers the Membership usage
    │   ├── given the listingTimestamp is not zero
    │   │   ├── given block timestamp is lower than tgeTimestamp
    │   │   │   ├── it refunds to prevent griefing
    │   │   │   ├── it emits Refunded event with Membership id and amountA
    │   │   │   ├── it transfers tokenB to the caller
    │   │   │   ├── it increases roundLiquidityA
    │   │   │   └── it lowers the Membership usage
    │   │   ├── given block timestamp is after or at tgeTimestamp and before the listingTimestamp
    │   │   │   └── it reverts
    │   │   └── given block timestamp is after or at listingTimestamp
    │   │       └── it reverts
    │   ├── given the round state is sale
    │   │   ├── it lowers liquidityB
    │   │   └── it does not change claimedBackTokenB
    │   └── given the round state is not sale
    │       ├── it increases claimedBackTokenB
    │       └── it does not change liquidityB
    └── given claimbackPeriod greater than zero
        ├── given emergency claimbacks are disabled and block timestamp greater than the sum of listingTimestamp and presale claimbackPeriod
        │   └── it reverts because the refunds period passed
        ├── given block timestamp lower than or equal to the sum of listingTimestamp and presale claimbackPeriod
        │   ├── given the Membership was used to claim tokenA
        │   │   └── it reverts
        │   ├── when the caller refunds their entire usage
        │   │   ├── it refunds
        │   │   └── it emits Refunded event with Membership id and amountA
        │   └── given emergency claimbacks are disabled and block timestamp greater than the sum of listingTimestamp and membership claimbackPeriod
        │       └── it reverts because the refunds period passed
        └── given emergency claimbacks are disabled and block timestamp greater than the sum of listingTimestamp and presale claimbackPeriod and membership claimbackPeriod is greater than presale claimbackPeriod
            └── it reverts because the refunds period passed
