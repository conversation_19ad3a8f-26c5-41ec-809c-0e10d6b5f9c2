// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_updateRound
├── when the caller is not a manager
│   └── it reverts
└── when the caller is a manager
    ├── given the round does not exist
    │   └── it reverts
    └── given the round exists
        ├── when startTimestamp equal to or greater than endTimestamp
        │   └── it reverts
        ├── when whitelistRoot and attributes are not empty
        │   └── it reverts
        ├── when invalid bonus values
        │   └── when denominator is zero
        │       └── it reverts
        └── when valid round config
            ├── when round starts in less than an hour
            │   └── it reverts
            └── when round starts later than in an hour
                ├── it updates the round
                ├── it emits RoundUpdated event with roundId
                ├── it updates bonus values correctly
                └── it verifies bonus values after update
