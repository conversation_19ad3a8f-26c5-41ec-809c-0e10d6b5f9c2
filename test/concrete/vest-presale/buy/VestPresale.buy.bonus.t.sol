// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestTest, Configuration } from "test/VestTest.sol";
import { Round, RoundState } from "src/types/Round.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "test/utils/Participants.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { WETHSample } from "test/samples/WETHSample.sol";

contract Presale_buy_bonus is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;
    using Participants for Participants.Collection;

    address internal charlie = address(0xC8A211E);

    uint256 internal roundId = 1;
    uint256 internal membershipId;
    Participants.Participant internal participant;

    // Base allocation and price (for non-bonus users)
    uint256 internal baseAllocation = 100 ether.decimals(decimalsA);
    uint256 internal basePrice = 0.1e18.decimals(decimalsB);
    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: basePrice,
        allocation: baseAllocation,
        claimbackPeriod: 7 days,
        tgeNumerator: 10,
        tgeDenominator: 100,
        cliffDuration: 0,
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 10,
        vestingPeriodDuration: 1 days,
        tradeable: 1
    });

    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();
        configuration.presale.tokenB = tokenB = new WETHSample();

        Round[] memory rounds = new Round[](1);

        rounds[0] = Round({
            name: "ROUND",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: abi.encode(attributes)
        });

        fixture(configuration, rounds);

        // Clear and set up our test round
        participants.clear(roundId);

        // Calculate and add user with 50% bonus (alice)
        uint256 baseCost = baseAllocation * basePrice / 10 ** decimalsA;
        uint256 halfBonusPrice = (basePrice * 2) / 3;
        uint256 halfBonusAllocation = (baseCost * 10 ** decimalsA) / halfBonusPrice;
        participants.add(
            roundId,
            alice,
            halfBonusPrice,
            halfBonusAllocation,
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );

        // Calculate and add user with 100% bonus (charlie)
        uint256 fullBonusPrice = basePrice / 2;
        uint256 fullBonusAllocation = (baseCost * 10 ** decimalsA) / fullBonusPrice;
        participants.add(
            roundId,
            charlie,
            fullBonusPrice,
            fullBonusAllocation,
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );

        // Update whitelist with bonus participants
        vm.startPrank(manager);
        presale.updateRoundWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        // Deposit enough tokens for all users:
        // 1. Bob: baseAllocation
        // 2. Alice: halfBonusAllocation
        // 3. Charlie: fullBonusAllocation
        uint256 totalAllocation = baseAllocation + halfBonusAllocation + fullBonusAllocation;
        vm.startPrank(address(this));
        script.deposit(roundId, totalAllocation);
        vm.stopPrank();
    }

    modifier givenARoundIsInSaleState() {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.startTimestamp);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.SALE), "round is in the sale state");
        _;
    }

    function _toAttributes(Participants.Participant memory participant_)
        private
        pure
        returns (IVestMembership.Attributes memory)
    {
        return IVestMembership.Attributes({
            price: participant_.price,
            allocation: participant_.allocation,
            claimbackPeriod: participant_.claimbackPeriod,
            tgeNumerator: participant_.tgeNumerator,
            tgeDenominator: participant_.tgeDenominator,
            cliffDuration: participant_.cliffDuration,
            cliffNumerator: participant_.cliffNumerator,
            cliffDenominator: participant_.cliffDenominator,
            vestingPeriodCount: participant_.vestingPeriodCount,
            vestingPeriodDuration: participant_.vestingPeriodDuration,
            tradeable: participant_.tradeable
        });
    }

    function test_WhenUserBuysWithZeroBonus() external givenARoundIsInSaleState {
        // Use base allocation and price from round attributes
        uint256 amountA = baseAllocation;
        uint256 amountB = amountA * basePrice / 10 ** decimalsA;
        uint256 baseCost = baseAllocation * basePrice / 10 ** decimalsA;
        assertApproxEqAbs(amountB, baseCost, 1, "cost should equal base cost");

        // Store initial liquidity
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // Transfer tokens to buyer
        tokenB.safeTransfer(bob, amountB);
        vm.startPrank(bob);
        tokenB.approve(address(presale), amountB);

        // Non-bonus users use round attributes directly (no proof needed)
        membershipId = presale.buy(roundId, amountA);
        
        // Verify membership was created
        assertEq(membership.ownerOf(membershipId), bob, "membership owner is bob");
        
        // Get membership attributes
        IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
        assertEq(memberAttrs.allocation, amountA, "allocation should be base amount");

        // it updates liquidity correctly
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(liquidityAAfter, liquidityABefore - amountA, "liquidity should decrease by allocation");

        vm.stopPrank();
    }

    function test_WhenUserBuysWithHalfBonus() external givenARoundIsInSaleState {
        // Clear existing participants
        participants.clear(roundId);

        // Calculate and add user with 50% bonus (alice)
        uint256 baseCost = baseAllocation * basePrice / 10 ** decimalsA;
        uint256 halfBonusPrice = (basePrice * 2) / 3;
        uint256 halfBonusAllocation = (baseCost * 10 ** decimalsA) / halfBonusPrice;
        participants.add(
            roundId,
            alice,
            halfBonusPrice,
            halfBonusAllocation,
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );

        // Update the round's whitelist with our new participants
        vm.startPrank(manager);
        presale.updateRoundWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        participant = participants.get(roundId, alice);
        uint256 amountA = participant.allocation;
        uint256 amountB = amountA * participant.price / 10 ** decimalsA;

        // Store initial liquidity
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // Transfer tokens to buyer
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.approve(address(presale), amountB);

        // it calculates allocation correctly with bonus
        membershipId = presale.buy(roundId, amountA, _toAttributes(participant), participant.proof);
        
        // Verify membership was created
        assertEq(membership.ownerOf(membershipId), alice, "membership owner is alice");
        
        // Get membership attributes
        IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
        assertEq(memberAttrs.allocation, halfBonusAllocation, "allocation should include 50% bonus");

        // it updates liquidity correctly with bonus
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(liquidityAAfter, liquidityABefore - halfBonusAllocation, "liquidity should decrease by allocation with bonus");

        vm.stopPrank();
    }

    function test_WhenUserBuysWithFullBonus() external givenARoundIsInSaleState {
        // Clear existing participants
        participants.clear(roundId);

        // Calculate and add user with 100% bonus (charlie)
        uint256 baseCost = baseAllocation * basePrice / 10 ** decimalsA;
        uint256 fullBonusPrice = basePrice / 2;
        uint256 fullBonusAllocation = (baseCost * 10 ** decimalsA) / fullBonusPrice;
        participants.add(
            roundId,
            charlie,
            fullBonusPrice,
            fullBonusAllocation,
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );

        // Update the round's whitelist with our new participants
        vm.startPrank(manager);
        presale.updateRoundWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        participant = participants.get(roundId, charlie);
        uint256 amountA = participant.allocation;
        uint256 amountB = amountA * participant.price / 10 ** decimalsA;

        // Store initial liquidity
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // Transfer tokens to buyer
        tokenB.safeTransfer(charlie, amountB);
        vm.startPrank(charlie);
        tokenB.approve(address(presale), amountB);

        // it calculates allocation correctly with bonus
        membershipId = presale.buy(roundId, amountA, _toAttributes(participant), participant.proof);
        
        // Verify membership was created
        assertEq(membership.ownerOf(membershipId), charlie, "membership owner is charlie");
        
        // Get membership attributes
        IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
        assertEq(memberAttrs.allocation, fullBonusAllocation, "allocation should include 100% bonus");

        // it updates liquidity correctly with bonus
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(liquidityAAfter, liquidityABefore - fullBonusAllocation, "liquidity should decrease by allocation with bonus");

        vm.stopPrank();
    }

    function test_WhenUserBuysWith150percentBonus() external givenARoundIsInSaleState {
        // Clear existing participants
        participants.clear(roundId);

        // Calculate allocation and price for 150% bonus
        uint256 baseCost = baseAllocation * basePrice / 10 ** decimalsA;
        uint256 bonus150Price = (basePrice * 2) / 5; // 40% of base price for 150% bonus
        uint256 bonus150Allocation = (baseCost * 10 ** decimalsA) / bonus150Price;
        
        // Store initial liquidity
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // Add participant with 150% bonus
        participants.add(
            roundId,
            charlie,
            bonus150Price,
            bonus150Allocation,
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );

        // Update the round's whitelist with our new participants
        vm.startPrank(manager);
        presale.updateRoundWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        participant = participants.get(roundId, charlie);
        uint256 amountA = participant.allocation;
        uint256 amountB = amountA * participant.price / 10 ** decimalsA;

        // Transfer tokens to buyer
        tokenB.safeTransfer(charlie, amountB);
        vm.startPrank(charlie);
        tokenB.approve(address(presale), amountB);

        // it calculates allocation correctly with 150% bonus
        membershipId = presale.buy(roundId, amountA, _toAttributes(participant), participant.proof);
        
        // Verify membership was created
        assertEq(membership.ownerOf(membershipId), charlie, "membership owner is charlie");
        
        // Get membership attributes and verify allocation
        IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
        assertEq(memberAttrs.allocation, bonus150Allocation, "allocation should include 150% bonus");

        // it updates liquidity correctly with bonus
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(liquidityAAfter, liquidityABefore - bonus150Allocation, "liquidity should decrease by allocation with bonus");

        vm.stopPrank();
    }

    function test_WhenMultipleUsersBuyWithDifferentBonusValues() external givenARoundIsInSaleState {
        uint256 baseCost = baseAllocation * basePrice / 10 ** decimalsA;
        uint256 tolerance = baseAllocation / 1e5; // 0.001% tolerance

        // Store initial liquidity
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);

        // Test bob's purchase (no bonus, using round attributes)
        {
            uint256 amountB = baseAllocation * basePrice / 10 ** decimalsA;
            assertApproxEqAbs(amountB, baseCost, 1, "bob's cost should equal base cost");

            tokenB.safeTransfer(bob, amountB);
            vm.startPrank(bob);
            tokenB.approve(address(presale), amountB);
            membershipId = presale.buy(roundId, baseAllocation);
            vm.stopPrank();

            IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
            assertEq(memberAttrs.allocation, baseAllocation, "bob's allocation should be base amount");
        }

        // Test alice's purchase (50% bonus)
        {
            participant = participants.get(roundId, alice);
            uint256 amountA = participant.allocation;
            uint256 amountB = amountA * participant.price / 10 ** decimalsA;
            assertApproxEqAbs(amountB, baseCost, 1, "alice's cost should equal base cost");

            tokenB.safeTransfer(alice, amountB);
            vm.startPrank(alice);
            tokenB.approve(address(presale), amountB);
            membershipId = presale.buy(roundId, amountA, _toAttributes(participant), participant.proof);
            vm.stopPrank();

            IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
            assertApproxEqAbs(memberAttrs.allocation, amountA, tolerance, "alice's allocation should include 50% bonus");
        }

        // Test charlie's purchase (100% bonus)
        {
            participant = participants.get(roundId, charlie);
            uint256 amountA = participant.allocation;
            uint256 amountB = amountA * participant.price / 10 ** decimalsA;
            assertApproxEqAbs(amountB, baseCost, 1, "charlie's cost should equal base cost");

            tokenB.safeTransfer(charlie, amountB);
            vm.startPrank(charlie);
            tokenB.approve(address(presale), amountB);
            membershipId = presale.buy(roundId, amountA, _toAttributes(participant), participant.proof);
            vm.stopPrank();

            IVestMembership.Attributes memory memberAttrs = membership.getAttributes(membershipId);
            assertApproxEqAbs(memberAttrs.allocation, amountA, tolerance, "charlie's allocation should include 100% bonus");
        }

        // Get final allocations for bonus users
        Participants.Participant memory aliceParticipant = participants.get(roundId, alice);
        Participants.Participant memory charlieParticipant = participants.get(roundId, charlie);

        // Verify final liquidity reflects all purchases
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(
            liquidityAAfter, 
            liquidityABefore - baseAllocation - aliceParticipant.allocation - charlieParticipant.allocation, 
            "liquidity should decrease by sum of all allocations"
        );
    }
}
