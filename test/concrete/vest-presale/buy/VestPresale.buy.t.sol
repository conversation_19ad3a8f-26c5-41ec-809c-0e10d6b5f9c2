// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { Round, RoundState } from "src/types/Round.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";
import { VestTest, Configuration } from "test/VestTest.sol";

import { Uint256Helper } from "test/utils/Uint256Helper.sol";

import { WETHSample } from "test/samples/WETHSample.sol";
import { EmptyContract } from "test/samples/EmptyContract.sol";
import { ERC721RevertingReceiver } from "test/samples/ERC721RevertingReceiver.sol";
import { ERC721AcceptingReceiver } from "test/samples/ERC721AcceptingReceiver.sol";
import { ERC721ReentrantReceiver } from "test/samples/ERC721ReentrantReceiver.sol";

contract Presale_buy is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;

    uint256 internal roundId = 1;

    uint256 internal membershipId;

    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: 0.1e18.decimals(decimalsB),
        allocation: 100 ether.decimals(decimalsA),
        claimbackPeriod: 7 days,
        tgeNumerator: 10,
        tgeDenominator: 100,
        cliffDuration: 0,
        cliffNumerator: 0,
        cliffDenominator: 100,
        vestingPeriodCount: 10,
        vestingPeriodDuration: 1 days,
        tradeable: 1
    });

    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();

        configuration.presale.tokenB = tokenB = new WETHSample();

        Round[] memory rounds = new Round[](1);

        rounds[0] = Round({
            name: "ROUND",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: abi.encode(attributes)
        });

        fixture(configuration, rounds);

        vm.startPrank(address(this));
        script.deposit(roundId, attributes.allocation * 2);
        vm.stopPrank();
    }

    function test_GivenARoundIsInPendingState() external {
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.PENDING), "the round is in pending state");
        vm.prank(alice);
        // it a user cannot buy
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.RoundStateMismatch.selector, roundId, RoundState.PENDING, RoundState.SALE
            )
        );
        presale.buy(roundId, 50 ether);
    }

    function test_GivenARoundIsInVestingState() external {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.endTimestamp + 1);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.VESTING), "the round is in vesting state");
        vm.prank(alice);
        // it a user cannot buy
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.RoundStateMismatch.selector, roundId, RoundState.VESTING, RoundState.SALE
            )
        );
        presale.buy(roundId, attributes.allocation);
    }

    modifier givenARoundIsInSaleState() {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.startTimestamp);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.SALE), "round 1 is in the sale state");
        _;
    }

    function test_WhenARoundHasNoAttributes() external givenARoundIsInSaleState {
        Round memory round = Round({
            name: "ROUND",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: ""
        });

        vm.warp(round.startTimestamp - 2 hours);

        vm.prank(manager);
        presale.updateRound(roundId, round);

        vm.warp(round.startTimestamp);

        vm.startPrank(alice);
        // it reverts
        vm.expectRevert();
        presale.buy(roundId, 0);
    }

    function test_WhenAUserPurchasesZeroTokens() external givenARoundIsInSaleState {
        vm.startPrank(alice);
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.buy(roundId, 0);
    }

    function test_WhenAUserPurchasesFromANonExistentRound() external givenARoundIsInSaleState {
        vm.startPrank(alice);
        uint256 nonExistentRoundId = 4;
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, nonExistentRoundId));
        presale.buy(nonExistentRoundId, attributes.allocation);
    }

    function test_WhenAUserPurchasesMoreTokensThanTheirAllocation() external givenARoundIsInSaleState {
        vm.startPrank(alice);
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.buy(roundId, attributes.allocation + 1);
    }

    function test_GivenTheUserDoesNotHaveEnoughTokenB() external givenARoundIsInSaleState {
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;
        uint256 balanceB = amountB - 1;
        tokenB.safeTransfer(alice, balanceB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientBalance)
        presale.buy(roundId, attributes.allocation);
    }

    function test_WhenTheUserCallsBuyMethodAfterASuccessfulFirstBuy() external givenARoundIsInSaleState {
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        presale.buy(roundId, attributes.allocation / 2);
        // it reverts because proofs are single use
        vm.expectRevert(abi.encodeWithSelector(VestPresale.AlreadyRoundParticipant.selector, roundId, alice));
        presale.buy(roundId, attributes.allocation / 2);
    }

    function test_WhenTheUserBuysLessThanTheirAllocation() external givenARoundIsInSaleState {
        uint256 amountA = attributes.allocation / 2;
        uint256 amountB = amountA * attributes.price / 10 ** decimalsA;
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        uint256 publicId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, amountA, block.timestamp)),
            abi.encode(IVestMembership.Usage({ max: amountA, current: 0 }))
        );
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, publicId);
        membershipId = presale.buy(roundId, amountA);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals the amount bought");
        assertEq(usage.max, amountA, "usage.max equals participant allocation");
        IVestMembership.Attributes memory participant = membership.getAttributes(membershipId);
        assertEq(participant.allocation, attributes.allocation, "Membership allocation equals participant allocation");
        assertEq(participant.price, attributes.price, "Membership price equals participant price");
        assertEq(
            participant.cliffDuration,
            attributes.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            participant.cliffNumerator,
            attributes.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            participant.cliffDenominator,
            attributes.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            participant.claimbackPeriod,
            attributes.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            participant.tgeNumerator, attributes.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            participant.tgeDenominator,
            attributes.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            participant.vestingPeriodCount,
            attributes.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            participant.vestingPeriodDuration,
            attributes.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        // liquidityA decreases
        assertEq(liquidityAAfter, liquidityABefore - amountA, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
        // it user does not receive any of the tokenA
        assertEq(tokenA.balanceOf(alice), 0, "alice has zero tokenA");
    }

    function test_WhenTheUserBuysTheirEntireAllocation() external givenARoundIsInSaleState {
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, attributes.allocation, block.timestamp)),
            abi.encodePacked(attributes.allocation, attributes.allocation, false)
        );
        vm.expectEmit(false, false, false, true);
        emit IERC721.Transfer(address(0), alice, membershipId);
        membershipId = presale.buy(roundId, attributes.allocation);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals the amount claimed");
        assertEq(usage.max, attributes.allocation, "usage.max equals participant allocation");
        IVestMembership.Attributes memory participant = membership.getAttributes(membershipId);
        assertEq(participant.allocation, attributes.allocation, "Membership allocation equals participant allocation");
        assertEq(participant.price, attributes.price, "Membership price equals participant price");
        assertEq(
            participant.cliffDuration,
            attributes.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            participant.cliffNumerator,
            attributes.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            participant.cliffDenominator,
            attributes.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            participant.claimbackPeriod,
            attributes.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            participant.tgeNumerator, attributes.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            participant.tgeDenominator,
            attributes.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            participant.vestingPeriodCount,
            attributes.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            participant.vestingPeriodDuration,
            attributes.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        // liquidityA decreases
        assertEq(liquidityAAfter, liquidityABefore - attributes.allocation, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
    }

    modifier givenRoundTokenALiquidityIsLowerThanUsersAllocation() {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 excessiveLiquidityA = liquidityABefore - attributes.allocation;
        vm.prank(beneficiary);
        presale.withdrawTokenA(roundId, excessiveLiquidityA + 1);
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertLt(liquidityAAfter, attributes.allocation, unicode"liquidity is lower than user’s allocation");
        _;
    }

    function test_WhenTheUserBuysLessOrEqualThanAvailableLiquidity()
        external
        givenARoundIsInSaleState
        givenRoundTokenALiquidityIsLowerThanUsersAllocation
    {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 amountB = liquidityABefore * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, liquidityABefore, block.timestamp)),
            abi.encode(IVestMembership.Usage({ current: 0, max: liquidityABefore }))
        );
        vm.expectEmit(true, true, false, true);
        emit IERC20.Transfer(alice, address(presale), amountB);
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, membershipId);
        membershipId = presale.buy(roundId, liquidityABefore);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals zero");
        assertEq(usage.max, liquidityABefore, "usage.max equals amount bought");
        IVestMembership.Attributes memory participant = membership.getAttributes(membershipId);
        assertEq(participant.allocation, attributes.allocation, "Membership allocation equals participant allocation");
        assertEq(participant.price, attributes.price, "Membership price equals participant price");
        assertEq(
            participant.cliffDuration,
            attributes.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            participant.cliffNumerator,
            attributes.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            participant.cliffDenominator,
            attributes.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            participant.claimbackPeriod,
            attributes.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            participant.tgeNumerator, attributes.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            participant.tgeDenominator,
            attributes.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            participant.vestingPeriodCount,
            attributes.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            participant.vestingPeriodDuration,
            attributes.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        // liquidityA decreases
        assertEq(liquidityAAfter, 0, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
    }

    function test_WhenTheUserBuysMoreThanAvailableLiquidity()
        external
        givenARoundIsInSaleState
        givenRoundTokenALiquidityIsLowerThanUsersAllocation
    {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 amountB = liquidityABefore * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        uint256 publicId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, attributes.allocation - 1, block.timestamp)),
            abi.encode(IVestMembership.Usage({ max: attributes.allocation - 1, current: 0 }))
        );
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, publicId);
        membershipId = presale.buy(roundId, attributes.allocation);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals the claimed amount");
        assertEq(usage.max, attributes.allocation - 1, "usage.max equals participant allocation");
        IVestMembership.Attributes memory participant = membership.getAttributes(membershipId);
        assertEq(participant.allocation, attributes.allocation, "Membership allocation equals participant allocation");
        assertEq(participant.price, attributes.price, "Membership price equals participant price");
        assertEq(
            participant.cliffDuration,
            attributes.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            participant.cliffNumerator,
            attributes.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            participant.cliffDenominator,
            attributes.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            participant.claimbackPeriod,
            attributes.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            participant.tgeNumerator, attributes.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            participant.tgeDenominator,
            attributes.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            participant.vestingPeriodCount,
            attributes.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            participant.vestingPeriodDuration,
            attributes.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        // liquidityA decreases
        assertEq(liquidityAAfter, 0, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
    }

    function test_WhenTheUserBuysMoreOrEqualThanAvailableLiquidityAndGotSoldCheckIsFalse()
        external
        givenARoundIsInSaleState
        givenRoundTokenALiquidityIsLowerThanUsersAllocation
    {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 amountB = liquidityABefore * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = presale.buy(roundId, attributes.allocation);

        (uint256 liquidityAAfter, bool gotSoldAfter) = presale.roundLiquidityA(roundId);

        assertEq(liquidityAAfter, 0, "liquidityA decreases");

        // it got sold check is true
        assertTrue(gotSoldAfter, "gotSoldAfter is true");
    }

    function test_WhenTheUserBuysMoreOrEqualThanAvailableLiquidityAndGotSoldCheckIsTrue()
        external
        givenARoundIsInSaleState
        givenRoundTokenALiquidityIsLowerThanUsersAllocation
    {
        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 amountB = liquidityABefore * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = presale.buy(roundId, attributes.allocation);
        vm.stopPrank();

        (uint256 liquidityAAfter, bool gotSoldAfter) = presale.roundLiquidityA(roundId);

        assertEq(liquidityAAfter, 0, "liquidityA decreases");

        // it got sold check is true
        assertTrue(gotSoldAfter, "gotSoldAfter is true");

        fixture_claimback(membershipId);

        (uint256 liquidityAAfterClaimback, bool gotSoldAfterClaimback) = presale.roundLiquidityA(roundId);

        assert(liquidityAAfterClaimback > 0);

        // it got sold check has not changed
        assertTrue(gotSoldAfterClaimback, "gotSoldAfter is true");
    }

    modifier givenAUserIsASmartContract() {
        _;
    }

    function test_GivenAUserDoesNotImplementTheHook() external givenARoundIsInSaleState givenAUserIsASmartContract {
        address smartContract = address(new EmptyContract());
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(smartContract, amountB);
        vm.startPrank(smartContract);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, smartContract));
        presale.buy(roundId, attributes.allocation);
    }

    modifier givenAUserDoesImplementTheHook() {
        _;
    }

    function test_WhenAUserReverts()
        external
        givenARoundIsInSaleState
        givenAUserIsASmartContract
        givenAUserDoesImplementTheHook
    {
        address smartContract = address(new ERC721RevertingReceiver());
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(smartContract, amountB);
        vm.startPrank(smartContract);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, smartContract));
        presale.buy(roundId, attributes.allocation);
    }

    modifier whenAUserDoesNotRevert() {
        _;
    }

    function test_WhenThereIsAReentrancy()
        external
        givenARoundIsInSaleState
        givenAUserIsASmartContract
        givenAUserDoesImplementTheHook
        whenAUserDoesNotRevert
    {
        ERC721ReentrantReceiver eRC721ReentrantReceiver = new ERC721ReentrantReceiver();

        eRC721ReentrantReceiver.hydrate(
            address(presale), abi.encodeWithSignature("buy(uint256,uint256)", roundId, attributes.allocation)
        );

        uint256 amountB = (attributes.allocation * attributes.price / 10 ** decimalsA) * 2;
        tokenB.safeTransfer(address(eRC721ReentrantReceiver), amountB);

        vm.startPrank(address(eRC721ReentrantReceiver));
        tokenB.forceApprove(address(presale), amountB);

        // it reverts
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.AlreadyRoundParticipant.selector, roundId, address(eRC721ReentrantReceiver)
            )
        );
        presale.buy(roundId, attributes.allocation);
    }

    function test_WhenThereIsNoReentrancy()
        external
        givenARoundIsInSaleState
        givenAUserIsASmartContract
        givenAUserDoesImplementTheHook
        whenAUserDoesNotRevert
    {
        address smartContract = address(new ERC721AcceptingReceiver());
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;
        tokenB.safeTransfer(smartContract, amountB);
        vm.startPrank(smartContract);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = presale.buy(roundId, attributes.allocation);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(smartContract), 0, "smart contract spent tokenB");
        assertEq(tokenB.balanceOf(address(presale)), presale.liquidityB(), "presale liquidity increased");
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), smartContract, "smart contract owns the Membership");
    }

    modifier whenAUserTransfersNativeCoin() {
        _;
    }

    function test_WhenTheUserTransfersLessCoinsThanAmountA()
        external
        givenARoundIsInSaleState
        whenAUserTransfersNativeCoin
    {
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;

        deal(alice, amountB);

        vm.startPrank(alice);

        // it reverts
        vm.expectRevert(Errors.Forbidden.selector);

        membershipId = presale.buy{ value: amountB / 2 }(roundId, attributes.allocation);
    }

    function test_WhenTheUserTransfersLessCoinsThanTheirAllocation()
        external
        givenARoundIsInSaleState
        whenAUserTransfersNativeCoin
    {
        uint256 amountA = attributes.allocation / 2;
        uint256 amountB = amountA * attributes.price / 10 ** decimalsA;

        deal(alice, amountB);

        vm.startPrank(alice);

        uint256 balanceB = tokenB.balanceOf(address(presale));

        membershipId = presale.buy{ value: amountB }(roundId, amountA);

        assertEq(alice.balance, 0, "it deposits native coin");
        assertEq(tokenB.balanceOf(address(presale)), balanceB + amountB, "it pays with tokenB");

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.current, 0, "usage.current equals the amount bought");
        assertEq(usage.max, amountA, "usage.max equals participant allocation");
    }

    function test_WhenTheUserTransfersExactlyTheSameAmountOfCoinsAsTheirAllocation()
        external
        givenARoundIsInSaleState
        whenAUserTransfersNativeCoin
    {
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;

        deal(alice, amountB);

        vm.startPrank(alice);

        uint256 balanceB = tokenB.balanceOf(address(presale));

        membershipId = presale.buy{ value: amountB }(roundId, attributes.allocation);

        assertEq(alice.balance, 0, "it deposits native coin");
        assertEq(tokenB.balanceOf(address(presale)), balanceB + amountB, "it pays with tokenB");

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.current, 0, "usage.current equals the amount bought");
        assertEq(usage.max, attributes.allocation, "usage.max equals participant allocation");
    }

    function test_WhenTheUserTransfersMoreCoinsThanTheirAllocation()
        external
        givenARoundIsInSaleState
        whenAUserTransfersNativeCoin
    {
        uint256 amountB = attributes.allocation * attributes.price / 10 ** decimalsA;

        deal(alice, amountB * 2);

        vm.startPrank(alice);

        uint256 balanceB = tokenB.balanceOf(address(presale));

        membershipId = presale.buy{ value: amountB * 2 }(roundId, attributes.allocation);

        assertEq(alice.balance, amountB, "it deposits native coin and sends back excess");
        assertEq(tokenB.balanceOf(address(presale)), balanceB + amountB, "it pays with tokenB");

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.current, 0, "usage.current equals the amount bought");
        assertEq(usage.max, attributes.allocation, "usage.max equals participant allocation");
    }
}
