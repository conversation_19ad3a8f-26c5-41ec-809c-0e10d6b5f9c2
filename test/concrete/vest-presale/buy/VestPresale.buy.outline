Presale_buy
  given a round is in pending state
    it a user cannot buy
  given a round is in vesting state
    it a user cannot buy
  given a round is in sale state
    when a round has no attributes
      it reverts
    when a user purchases zero tokens
      it reverts
    when a user purchases from a non existent round
      it reverts
    when a user purchases more tokens than their allocation
      it reverts
    given the user does not have enough tokenB
      it reverts
    when the user calls buy method after a successful first buy
      it reverts because proofs are single use
    when the user buys less than their allocation
      it pays with tokenB
      it mints Membership with correct attributes and usage
      it the caller owns the Membership
      it emits the Transfer event
      it user does not receive any of the tokenA
    when the user buys their entire allocation
      it pays with tokenB
      it mints Membership with correct attributes and usage
      it the caller owns the Membership
      it emits the Transfer event
    given round tokenA liquidity is lower than user's allocation
      when the user buys less or equal than available liquidity
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
        it emits the Transfer event
      when the user buys more than available liquidity
        it user buys out the entire liquidity
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
        it emits the Transfer event
      when the user buys more or equal than available liquidity and got sold check is false
        it user buys out the entire liquidity
        it got sold check is true
      when the user buys more or equal than available liquidity and got sold check is true
        it user buys out the entire liquidity
        it got sold check has not changed
    given a user is a smart contract
      given a user does not implement the hook
        it reverts
      given a user does implement the hook
        when a user reverts
          it reverts
        when a user does not revert
          when there is a reentrancy
            it reverts
          when there is no reentrancy
            it pays with tokenB
            it mints Membership with correct attributes and usage
            it the caller owns the Membership
            it emits the Transfer event
    when a user transfers native coin
      when the user transfers less coins than amountA
        it reverts
      when the user transfers less coins than their allocation
        it deposits native coin
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
      when the user transfers exactly the same amount of coins as their allocation
        it deposits native coin
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
      when the user transfers more coins than their allocation
        it deposits native coin and sends back excess
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership


