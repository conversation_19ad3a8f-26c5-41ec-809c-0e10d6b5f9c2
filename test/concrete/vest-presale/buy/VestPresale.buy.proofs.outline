Presale_buy_proofs
  given a round is in pending state
    it a user cannot buy
  given a round is in vesting state
    it a user cannot buy
  given a round is in sale state
    when a user manipulates their price
      it reverts
    when a user manipulates their allocation
      it reverts
    when a user manipulates their tgeNumerator
      it reverts
    when a user manipulates their tgeDenominator
      it reverts
    when tgeDenominator equals zero
      it reverts because division by zero is not allowed
    when a user manipulates their cliffDuration
      it reverts
    when a user manipulates their cliffNumerator
      it reverts
    when a user manipulates their cliffDenominator
      it reverts
    when cliffDenominator equals zero
      it reverts because division by zero is not allowed
    when cliffNumerator is non zero and cliffDuration is zero
      it reverts because tgeNumerator should be used in that case
    when a user manipulates their vestingPeriodCount
      it reverts
    when a user manipulates their vestingPeriodDuration
      it reverts
    when vestingPeriodCount greater than zero and vestingPeriodDuration is zero
      it reverts
    when vestingPeriodCount is one
      it reverts because only the cliffNumerator should be used
    when no tgeNumerator and no cliffDuration and no vestingPeriodCount
      it reverts
    when vestingPeriodCount is zero and cliffNumerator greater than zero
      it reverts because all tokens will be released at the cliff end
    when cliffDuration is zero and vestingPeriodCount is zero and tgeNumerator does not equal tgeDenominator
      it reverts because all tokens must be released at tge
    when tge is greater than 100 percent
      it reverts
    when cliff height is greater than 100 percent
      it reverts
    when the sum of tge and cliff height is greater than 100 percent
      it reverts
    when an attacker uses alice attributes and proofs
      it reverts
    when a user purchases zero tokens
      it reverts
    when a user purchases from a non existent round
      it reverts
    when a user purchases more tokens than their allocation
      it reverts
    given tokenB has transfer fees
      it reverts
    when the caller is a user with zero price
      it receives Membership without the need for an allowance
      it their Membership reflects how much they bought and how much they can buy
    given the user did not give an allowance for tokenB
      it reverts
    given the user does not have enough tokenB
      it reverts
    when the user calls buy method after a successful first buy
      it reverts because proofs are single use
    when the user buys less than their allocation
      it pays with tokenB
      it mints Membership with correct attributes and usage
      it the caller owns the Membership
      it emits the Transfer event
      it user does not receive any of the tokenA
    when the user buys their entire allocation
      it pays with tokenB
      it mints Membership with correct attributes and usage
      it the caller owns the Membership
      it emits the Transfer event
    given round tokenA liquidity is lower than user's allocation
      when the user buys less or equal than available liquidity
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
        it emits the Transfer event
      when the user buys more than available liquidity
        it user buys out the entire liquidity
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
        it emits the Transfer event
      when the user buys more or equal than available liquidity and got sold check is false
        it user buys out the entire liquidity
        it got sold check is true
      when the user buys more or equal than available liquidity and sold got check is true
        it user buys out the entire liquidity
        it got sold check has not changed
    given a user is a smart contract
      given a user does not implement the hook
        it reverts
      given a user does implement the hook
        when a user reverts
          it reverts
        when a user does not revert
          when there is a reentrancy
            it reverts
          when there is no reentrancy
            it pays with tokenB
            it mints Membership with correct attributes and usage
            it the caller owns the Membership
            it emits the Transfer event
    given a user has multiple allocations
      it mints Membership for the first allocation
      it mints Membership for the second allocation
    when a user transfers native coin
      when the user transfers less coins than amountA
        it reverts
      when the user transfers less coins than their allocation
        it deposits native coin
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
      when the user transfers exactly the same amount of coins as their allocation
        it deposits native coin
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership
      when the user transfers more coins than their allocation
        it deposits native coin and sends back excess
        it pays with tokenB
        it mints Membership with correct attributes and usage
        it the caller owns the Membership


