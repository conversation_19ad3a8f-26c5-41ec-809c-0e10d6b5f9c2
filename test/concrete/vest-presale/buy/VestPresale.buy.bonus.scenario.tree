// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_buy_bonus_scenario
└── given a round with 15 percent bonus and 10000 base allocation
    └── when running full bonus scenario
        ├── it creates round with correct parameters
        ├── it allows 5 base users to buy 1000 tokens each
        ├── it allows 5 bonus users to buy 1150 tokens each
        ├── it tracks total liquidity correctly
        └── it returns remaining tokens to manager
