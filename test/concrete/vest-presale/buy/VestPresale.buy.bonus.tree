// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_buy_bonus
└── given a round is in sale state
    ├── when user buys with zero bonus
    │   ├── it calculates allocation correctly
    │   └── it updates liquidity correctly
    ├── when user buys with half bonus
    │   ├── it calculates allocation correctly with bonus
    │   └── it updates liquidity correctly with bonus
    ├── when user buys with full bonus
    │   ├── it calculates allocation correctly with max bonus
    │   └── it updates liquidity correctly with max bonus
    ├── when user buys with 150 percent bonus
    │   ├── it calculates allocation correctly with 150 percent bonus
    │   └── it updates liquidity correctly with 150 percent bonus
    └── when multiple users buy with different bonus values
        ├── it calculates allocations correctly for all users
        └── it updates total liquidity correctly
