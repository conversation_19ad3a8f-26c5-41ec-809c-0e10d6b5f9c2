// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestTest, Configuration } from "test/VestTest.sol";
import { Round, RoundState } from "src/types/Round.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "test/utils/Participants.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { WETHSample } from "test/samples/WETHSample.sol";
import { Presale } from "src/types/Configuration.sol";
import { Deductibles } from "src/types/Deductibles.sol";

contract Presale_buy_bonus_scenario is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;
    using Participants for Participants.Collection;

    uint256 internal roundId = 1;
    uint256 internal membershipId;
    Participants.Participant internal participant;

    // Base allocation and price
    uint256 internal baseAllocation = 1000 ether.decimals(decimalsA);
    uint256 internal basePrice = 0.1e18.decimals(decimalsB);
    uint256 internal totalBaseAllocation = 10000 ether.decimals(decimalsA);
    uint32 internal bonusNumerator = 15;
    uint32 internal bonusDenominator = 100;
    uint256 internal totalBonusAllocation;
    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: basePrice,
        allocation: baseAllocation,
        claimbackPeriod: 7 days,
        tgeNumerator: 10,
        tgeDenominator: 100,
        cliffDuration: 0,
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 10,
        vestingPeriodDuration: 1 days,
        tradeable: 1
    });

    // Users array for bulk operations
    address[] internal baseUsers;
    address[] internal bonusUsers;

    function _toAttributes(Participants.Participant memory p)
        internal
        pure
        returns (IVestMembership.Attributes memory)
    {
        return IVestMembership.Attributes({
            price: p.price,
            allocation: p.allocation,
            claimbackPeriod: p.claimbackPeriod,
            tgeNumerator: p.tgeNumerator,
            tgeDenominator: p.tgeDenominator,
            cliffDuration: p.cliffDuration,
            cliffNumerator: p.cliffNumerator,
            cliffDenominator: p.cliffDenominator,
            vestingPeriodCount: p.vestingPeriodCount,
            vestingPeriodDuration: p.vestingPeriodDuration,
            tradeable: p.tradeable
        });
    }

    modifier givenARoundWith15PercentBonusAnd10000BaseAllocation() {
        // Setup configuration
        Configuration memory configuration = composeConfiguration();
        configuration.presale.tokenB = tokenB = new WETHSample();

        // Calculate total allocation including bonus
        totalBonusAllocation = (totalBaseAllocation * (bonusNumerator + bonusDenominator)) / bonusDenominator;

        // Create round with 15% bonus
        Round[] memory rounds = new Round[](1);
        rounds[0] = Round({
            name: "ROUND",
            startTimestamp: 1739850477, // 2025-02-17T13:47:57+01:00
            endTimestamp: 1739850477 + 7 days,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: bonusNumerator,
            bonusDenominator: bonusDenominator,
            attributes: abi.encode(attributes)
        });

        fixture(configuration, rounds);

        // Set time to start of round
        vm.warp(rounds[0].startTimestamp);

        // Clear and set up our test round
        participants.clear(roundId);

        // Setup 5 base users
        baseUsers = new address[](5);
        for (uint256 i = 0; i < 5; i++) {
            baseUsers[i] = address(uint160(1000 + i));
        }

        // Setup 5 bonus users with 15% bonus
        bonusUsers = new address[](5);
        uint256 bonusPrice = (basePrice * bonusDenominator) / (bonusNumerator + bonusDenominator);
        uint256 bonusAllocation = baseAllocation * (bonusNumerator + bonusDenominator) / bonusDenominator;

        for (uint256 i = 0; i < 5; i++) {
            bonusUsers[i] = address(uint160(2000 + i));
            participants.add(
                roundId,
                bonusUsers[i],
                bonusPrice,
                bonusAllocation,
                7 days,
                10,
                100,
                0,
                0,
                1,
                10,
                1 days
            );
        }

        // Update whitelist with bonus participants
        vm.startPrank(manager);
        presale.updateRoundWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        // Deposit total allocation including bonus
        vm.startPrank(address(this));
        script.deposit(roundId, totalBonusAllocation);
        vm.stopPrank();

        _;
    }

    function test_WhenRunningFullBonusScenario() external givenARoundWith15PercentBonusAnd10000BaseAllocation {
        // Store initial liquidity
        (uint256 liquidityABefore, ) = presale.roundLiquidityA(roundId);

        // Process base users (no bonus)
        for (uint256 i = 0; i < baseUsers.length; i++) {
            address user = baseUsers[i];
            uint256 amountB = baseAllocation * basePrice / 10 ** decimalsA;

            tokenB.safeTransfer(user, amountB);
            vm.startPrank(user);
            tokenB.approve(address(presale), amountB);
            membershipId = presale.buy(roundId, baseAllocation);
            vm.stopPrank();

            // Verify membership
            assertEq(membership.ownerOf(membershipId), user, "membership owner should be base user");
            IVestMembership.Attributes memory attrs = membership.getAttributes(membershipId);
            assertEq(attrs.allocation, baseAllocation, "base user allocation should be correct");
        }

        // Calculate bonus allocation (same for all bonus users)
        uint256 bonusAllocation = baseAllocation * (bonusNumerator + bonusDenominator) / bonusDenominator;

        // Process bonus users (15% bonus)
        for (uint256 i = 0; i < bonusUsers.length; i++) {
            address user = bonusUsers[i];
            Participants.Participant memory p = participants.get(roundId, user);
            uint256 amountA = p.allocation;
            uint256 amountB = amountA * p.price / 10 ** decimalsA;

            tokenB.safeTransfer(user, amountB);
            vm.startPrank(user);
            tokenB.approve(address(presale), amountB);
            membershipId = presale.buy(roundId, amountA, _toAttributes(p), p.proof);
            vm.stopPrank();

            // Verify membership
            assertEq(membership.ownerOf(membershipId), user, "membership owner should be bonus user");
            IVestMembership.Attributes memory attrs = membership.getAttributes(membershipId);
            assertEq(attrs.allocation, amountA, "bonus user allocation should include 15% bonus");
        }

        // Verify final liquidity after all purchases
        (uint256 liquidityAAfter, ) = presale.roundLiquidityA(roundId);
        uint256 totalBaseUsed = baseAllocation * baseUsers.length;
        uint256 totalBonusUsed = bonusAllocation * bonusUsers.length;
        assertEq(
            liquidityAAfter,
            liquidityABefore - totalBaseUsed - totalBonusUsed,
            "liquidity should decrease by sum of all allocations"
        );

        // End round and verify manager withdrawal
        Round memory round = presale.getRound(roundId);
        vm.warp(round.endTimestamp + 1);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.VESTING), "round should be in vesting state");

        // Verify and withdraw remaining tokenA
        (uint256 remainingTokens, ) = presale.roundLiquidityA(roundId);
        uint256 expectedRemaining = totalBonusAllocation - totalBaseUsed - totalBonusUsed;
        assertEq(remainingTokens, expectedRemaining, "remaining tokens should match expected amount");

        vm.startPrank(beneficiary);
        presale.withdrawTokenA(roundId, remainingTokens);
        vm.stopPrank();
        
        (uint256 finalLiquidity, ) = presale.roundLiquidityA(roundId);
        assertEq(finalLiquidity, 0, "all remaining tokens should be withdrawn");

        // Set up timestamps for withdrawal
        vm.startPrank(manager);
        uint256 tgeTime = block.timestamp + 1 days;
        uint256 listingTime = tgeTime + 1 days;
        presale.updateTgeTimestamp(tgeTime);
        presale.updateListingTimestamp(listingTime);
        vm.stopPrank();

        // Wait for claimback period to end
        vm.warp(listingTime + 7 days + 1);

        // Calculate expected tokenB
        uint256 totalTokenB = presale.liquidityB();
        uint256 claimedBack = presale.claimedBackTokenB();
        (uint256 deductiblesAmount, , ) = presale.deductibles();
        uint256 expectedWithdrawable = totalTokenB - claimedBack - deductiblesAmount;

        // Calculate expected fee
        Presale.Fees memory fees = presale.getFees();
        uint256 expectedFee = (expectedWithdrawable * fees.tokenBNumerator) / fees.tokenBDenominator + deductiblesAmount;

        // Store balances before withdrawal
        uint256 beneficiaryBalanceBefore = tokenB.balanceOf(beneficiary);
        uint256 feeCollectorBalanceBefore = tokenB.balanceOf(presale.getFeeCollector());

        // Withdraw tokenB
        presale.withdrawTokenB();

        // Verify balances after withdrawal
        uint256 beneficiaryBalanceAfter = tokenB.balanceOf(beneficiary);
        uint256 feeCollectorBalanceAfter = tokenB.balanceOf(presale.getFeeCollector());

        assertEq(
            beneficiaryBalanceAfter - beneficiaryBalanceBefore,
            expectedWithdrawable - expectedFee,
            "beneficiary should receive correct amount"
        );
        assertEq(
            feeCollectorBalanceAfter - feeCollectorBalanceBefore,
            expectedFee,
            "fee collector should receive correct fee"
        );
    }
}
