// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_updateListingTimestamp
├── when the caller is not a manager
│   └── it reverts
└── when the caller is a manager
    ├── when new listingTimestamp in the past or present
    │   └── it reverts
    ├── when new listingTimestamp in the future
    │   ├── it sets new listingTimestamp
    │   ├── it emits ListingTimestampUpdated
    │   └── it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership
    ├── when tgeTimestamp equals zero
    │   └── it reverts
    ├── when new listingTimestamp is before tgeTimestamp
    │   └── it reverts
    ├── when new listingTimestamp is after tgeTimestamp
    │   ├── it modifies state
    │   ├── it emits ListingTimestampUpdated
    │   └── it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership
    ├── when block timestamp at or after listingTimestamp
    │   └── it reverts
    └── when new timestamp is greater than 30 days in the future
        ├── it emits
        ├── it tge timestamp is not updated
        └── it pending tge timestamp added
