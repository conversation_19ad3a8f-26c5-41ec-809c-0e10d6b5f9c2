// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";

import { VestTest, Configuration } from "test/VestTest.sol";

contract Presale_updateListingTimestamp is VestTest {
    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenTheCallerIsNotAManager() external {
        // it reverts
        vm.prank(alice);
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, alice));

        presale.updateListingTimestamp(vm.unixTime() * 2);
    }

    modifier whenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_WhenNewListingTimestampInThePastOrPresent() external whenTheCallerIsAManager {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(block.timestamp - 1);
    }

    function test_WhenNewListingTimestampInTheFuture() external whenTheCallerIsAManager {
        uint256 timestamp = presale.getTgeTimestamp() + 10 days;

        vm.warp(presale.getTgeTimestamp() - 1);

        // TODO
        // it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership

        // it emits ListingTimestampUpdated
        vm.expectEmit(true, false, false, false);
        emit VestPresale.ListingTimestampUpdated(timestamp);

        presale.updateListingTimestamp(timestamp);

        // it sets new listingTimestamp
        assertEq(presale.getListingTimestamp(), timestamp);
    }

    modifier whenTgeTimestampIsZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 0;

        presale = fixture(configuration);
        _;
    }

    function test_WhenTgeTimestampEqualsZero() external whenTgeTimestampIsZero whenTheCallerIsAManager {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(block.timestamp + 1);
    }

    function test_WhenNewListingTimestampIsBeforeTgeTimestamp() external whenTheCallerIsAManager {
        uint256 timestamp = presale.getTgeTimestamp() - 1;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(timestamp);
    }

    function test_WhenNewListingTimestampIsAfterTgeTimestamp() external whenTheCallerIsAManager {
        // TODO
        // it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership

        uint256 timestamp = presale.getTgeTimestamp() + 1;

        vm.warp(timestamp - 10 days);

        // it emits ListingTimestampUpdated
        vm.expectEmit(true, false, false, false);
        emit VestPresale.ListingTimestampUpdated(timestamp);
        presale.updateListingTimestamp(timestamp);

        // it modifies state
        assertEq(presale.getListingTimestamp(), timestamp);
    }

    function test_WhenBlockTimestampAtOrAfterListingTimestamp() external whenTheCallerIsAManager {
        // it reverts
        uint256 timestamp = vm.unixTime();

        presale.updateListingTimestamp(timestamp);

        vm.warp(timestamp);

        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(timestamp * 2);
    }

    function test_WhenNewTimestampIsGreaterThan30DaysInTheFuture() external whenTheCallerIsAManager {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = block.timestamp + 1 days;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration);

        // it emits
        vm.expectEmit(true, true, true, true);
        emit VestPresale.UpdateForListingTimestampRequested(block.timestamp + 31 days);

        vm.prank(manager);
        presale.updateListingTimestamp(block.timestamp + 31 days);

        // it listing timestamp is not updated
        assertEq(presale.getListingTimestamp(), 0);

        // it pending listing timestamp added
        (, uint256 pendingListingTimestamp) = presale.pendingTimestamps();
        assertEq(pendingListingTimestamp, block.timestamp + 31 days);
    }
}
