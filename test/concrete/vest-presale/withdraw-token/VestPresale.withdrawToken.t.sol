// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";

import { VestTest } from "test/VestTest.sol";
import { ERC20Sample } from "test/samples/ERC20Sample.sol";

contract Presale_withdrawToken is VestTest {
    ERC20Sample public sample = new ERC20Sample(18, 0);

    function setUp() public override {
        super.setUp();

        fixture();

        sample.transfer(address(presale), 1 ether);
    }

    function test_GivenTheCallerIsNotAManager() external {
        vm.prank(chuck);

        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        // it reverts
        presale.withdrawToken(chuck, sample, 1 ether);
    }

    modifier givenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_WhenTryingToWithdrawTokenA() external givenTheCallerIsAManager {
        vm.expectRevert(Errors.UnacceptableReference.selector);

        // it reverts
        presale.withdrawToken(manager, tokenA, 1 ether);
    }

    function test_WhenTryingToWithdrawTokenB() external givenTheCallerIsAManager {
        vm.expectRevert(Errors.UnacceptableReference.selector);

        // it reverts
        presale.withdrawToken(manager, tokenB, 1 ether);
    }

    function test_WhenTryingToWithdrawAnyOtherERC20(uint256 amount) external givenTheCallerIsAManager {
        vm.assume(amount <= 1 ether);

        presale.withdrawToken(manager, sample, amount);

        // it transfers desired amount of ERC20 token from the Presale smart contract to a specified address
        assertEq(sample.balanceOf(manager), amount, "transfer desired amount to manager");
    }
}
