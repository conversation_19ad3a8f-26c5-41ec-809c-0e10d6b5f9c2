// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_getRoundState
├── given round does not exist
│   └── it reverts
└── given round exists
    ├── when block timestamp is before round start timestamp
    │   └── it returns PENDING state
    ├── when block timestamp is between round start and end timestamp
    │   ├── given roundLiquidityA is greater than zero
    │   │   └── it returns SALE state
    │   ├── given roundLiquidityA is zero
    │   │   └── it returns VESTING state
    │   └── given roundLiquidityA has sold
    │       └── it returns VESTING state
    ├── when block timestamp is at end timestamp
    │   └── it returns VESTING state
    └── when block timestamp is after end timestamp
        └── it returns VESTING state
