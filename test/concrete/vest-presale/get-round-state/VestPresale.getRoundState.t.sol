// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestPresale } from "src/VestPresale.sol";
import { Round, RoundState } from "src/types/Round.sol";

import { VestTest } from "test/VestTest.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "../../../utils/Participants.sol";

contract Presale_getRoundState is VestTest {
    using Participants for Participants.Collection;

    using Uint256Helper for *;
    using SafeERC20 for IERC20;

    Round internal round;
    uint256 internal roundId = 1;

    uint256 liquidityForDistribution = 300 ether.decimals(decimalsA);

    function setUp() public override {
        super.setUp();

        Round[] memory rounds = new Round[](1);
        participants.clear(1);

        // tge: 10%
        participants.add(
            roundId,
            alice,
            0.1e18.decimals(decimalsB),
            liquidityForDistribution / 3,
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );

        // tge: 0%, price: 0
        participants.add(roundId, bob, 0, liquidityForDistribution / 3, 7 days, 0, 100, 0, 0, 1, 10, 1 days);

        // tge: 0%, cliff: 1 day
        participants.add(
            roundId,
            carol,
            0.1e18.decimals(decimalsB),
            liquidityForDistribution / 3,
            7 days,
            0,
            100,
            1 days,
            0,
            1,
            10,
            1 days
        );

        round = rounds[0] = Round({
            name: "ROUND I",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: participants.getWhitelistRoot(1),
            proofsUri: "",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: ""
        });

        fixture(rounds);
    }

    function test_GivenRoundDoesNotExist() external {
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, 2));

        // it reverts
        presale.getRound(2);
    }

    modifier givenRoundExists() {
        _;
    }

    function test_WhenBlockTimestampIsBeforeRoundStartTimestamp() external givenRoundExists {
        vm.warp(round.startTimestamp - 1);

        // it returns PENDING state
        RoundState state = presale.getRoundState(roundId);

        assertEq(uint256(state), uint256(RoundState.PENDING), "round has pending state");
    }

    modifier whenBlockTimestampIsBetweenRoundStartAndEndTimestamp() {
        vm.warp(round.startTimestamp + 1);
        _;
    }

    function test_GivenRoundLiquidityAIsGreaterThanZero()
        external
        givenRoundExists
        whenBlockTimestampIsBetweenRoundStartAndEndTimestamp
    {
        // it returns SALE state
        RoundState state = presale.getRoundState(roundId);

        assertEq(uint256(state), uint256(RoundState.SALE), "round has sale state");
    }

    function test_GivenRoundLiquidityAIsZero()
        external
        givenRoundExists
        whenBlockTimestampIsBetweenRoundStartAndEndTimestamp
    {
        vm.startPrank(beneficiary);

        (uint256 liquidityA,) = presale.roundLiquidityA(roundId);
        presale.withdrawTokenA(roundId, liquidityA);

        // it returns VESTING state
        RoundState state = presale.getRoundState(roundId);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }

    function test_GivenRoundLiquidityAHasSold()
        external
        givenRoundExists
        whenBlockTimestampIsBetweenRoundStartAndEndTimestamp
    {
        (uint256 liquidityABefore, bool gotSoldBefore) = presale.roundLiquidityA(roundId);

        assert(liquidityABefore > 0);
        assertFalse(gotSoldBefore);

        fixture_buy(roundId, alice);
        fixture_buy(roundId, bob);
        fixture_buy(roundId, carol);

        (uint256 liquidityAAfter, bool gotSoldAfter) = presale.roundLiquidityA(roundId);

        assertEq(liquidityAAfter, 0);
        assertTrue(gotSoldAfter);

        // it returns VESTING state
        RoundState state = presale.getRoundState(roundId);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }

    function test_WhenBlockTimestampIsAtEndTimestamp() external givenRoundExists {
        vm.warp(round.endTimestamp);

        // it returns VESTING state
        RoundState state = presale.getRoundState(roundId);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }

    function test_WhenBlockTimestampIsAfterEndTimestamp() external givenRoundExists {
        vm.warp(round.endTimestamp + 1);

        // it returns VESTING state
        RoundState state = presale.getRoundState(roundId);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }
}
