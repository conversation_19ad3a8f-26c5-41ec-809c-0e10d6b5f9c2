// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { Round } from "src/types/Round.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestTest } from "test/VestTest.sol";

contract Presale_addRound is VestTest {
    Round validRound;

    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenInvalidRoundConfig() external {
        uint256 timestamp = vm.unixTime();

        vm.startPrank(manager);

        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.addRound(
            Round({
                name: "invalid round",
                startTimestamp: timestamp,
                endTimestamp: timestamp - 1,
                whitelistRoot: "",
                proofsUri: "",
                bonusNumerator: 0,
                bonusDenominator: 100,
                attributes: ""
            })
        );

        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.addRound(
            Round({
                name: "",  // Empty name should revert
                startTimestamp: timestamp,
                endTimestamp: timestamp + 1,
                whitelistRoot: "",
                proofsUri: "",
                bonusNumerator: 0,
                bonusDenominator: 100,
                attributes: ""
            })
        );

        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.addRound(
            Round({
                name: "invalid round",
                startTimestamp: 0,  // Zero startTimestamp should revert
                endTimestamp: timestamp + 1,
                whitelistRoot: "",
                proofsUri: "",
                bonusNumerator: 0,
                bonusDenominator: 100,
                attributes: ""
            })
        );

        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.addRound(
            Round({
                name: "invalid round",
                startTimestamp: timestamp,
                endTimestamp: 0,  // Zero endTimestamp should revert
                whitelistRoot: "",
                proofsUri: "",
                bonusNumerator: 0,
                bonusDenominator: 100,
                attributes: ""
            })
        );
    }

    modifier whenInvalidBonusValues() {
        uint256 timestamp = vm.unixTime();
        vm.startPrank(manager);
        _;
    }

    function test_WhenDenominatorIsZero() external whenInvalidBonusValues {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.addRound(
            Round({
                name: "invalid round",
                startTimestamp: vm.unixTime(),
                endTimestamp: vm.unixTime() + 1,
                whitelistRoot: "",
                proofsUri: "",
                bonusNumerator: 10,
                bonusDenominator: 0,  // Zero denominator should revert
                attributes: ""
            })
        );
    }

    modifier whenValidRoundConfig() {
        validRound = Round({
            name: "valid round",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() + 1,
            whitelistRoot: keccak256(abi.encode(1)),
            proofsUri: "https://example.com",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: ""
        });
        _;
        delete validRound;
    }

    function test_WhenTheCallerIsAPresaleManager() external whenValidRoundConfig {
        vm.startPrank(manager);

        // it emits a {RoundUpdated} event
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(1);

        // it adds round
        presale.addRound(validRound);
        Round memory addedRound = presale.getRound(1);
        // it assigns round name
        assertEq(validRound.name, addedRound.name);
        // it assigns round startTimestamp
        assertEq(validRound.startTimestamp, addedRound.startTimestamp);
        // it assigns round endTimestamp
        assertEq(validRound.endTimestamp, addedRound.endTimestamp);
        // it assigns round whitelistRoot
        assertEq(validRound.whitelistRoot, addedRound.whitelistRoot);
        // it assigns round proofsURI
        assertEq(validRound.proofsUri, addedRound.proofsUri);
        // it assigns round bonusNumerator
        assertEq(validRound.bonusNumerator, addedRound.bonusNumerator);
        // it assigns round bonusDenominator
        assertEq(validRound.bonusDenominator, addedRound.bonusDenominator);
        // it increases next round id sequencer
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(2);
        presale.addRound(validRound);
    }

    function test_WhenTheCallerIsAnAttacker() external whenValidRoundConfig {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        vm.prank(chuck);
        presale.addRound(validRound);
    }
}
