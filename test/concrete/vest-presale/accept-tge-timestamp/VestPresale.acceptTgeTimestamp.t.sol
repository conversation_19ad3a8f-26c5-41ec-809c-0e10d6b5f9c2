// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Configuration } from "test/VestTest.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestTest } from "test/VestTest.sol";

contract VestPresaleacceptTgeTimestamp is VestTest {
    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration);
    }

    modifier whenIsPendingTimestamp() {
        vm.prank(manager);
        presale.updateTgeTimestamp(vm.unixTime() * 2);
        _;
    }

    function test_WhenNotBeneficiaryTryingToAcceptTgeTimestamp() external whenIsPendingTimestamp {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.acceptTgeTimestamp();
    }

    function test_WhenPendingTimestampEqualsZero() external {
        // it reverts
        vm.expectRevert(Errors.Forbidden.selector);

        vm.prank(beneficiary);
        presale.acceptTgeTimestamp();
    }

    function test_WhenTgeTimestampIsAccepted() external whenIsPendingTimestamp {
        (uint256 pendingTgeTimestampBefore,) = presale.pendingTimestamps();

        // it emits
        vm.expectEmit(true, true, true, true);
        emit VestPresale.TgeTimestampUpdated(pendingTgeTimestampBefore);

        vm.prank(beneficiary);
        presale.acceptTgeTimestamp();

        (uint256 pendingTgeTimestampAfter,) = presale.pendingTimestamps();

        // it pending timestamp equals zero
        assertEq(0, pendingTgeTimestampAfter);

        // it tge timestamp is updated
        assertEq(pendingTgeTimestampBefore, presale.getTgeTimestamp());
    }
}
