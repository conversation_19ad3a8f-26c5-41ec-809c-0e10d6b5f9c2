// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Deductibles } from "src/types/Deductibles.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestTest } from "test/VestTest.sol";

contract Presale_acceptDeductibles is VestTest {
    function setUp() public override {
        super.setUp();

        fixture();
    }

    modifier whenDeductiblesIsNotPending() {
        vm.prank(presale.manager());
        presale.updateDeductibles(1 ether);
        _;
    }

    function test_WhenTheCallerIsNotABeneficiary() external whenDeductiblesIsNotPending {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        presale.acceptDeductibles();
    }

    modifier whenTheCallerIsABeneficiary() {
        vm.startPrank(presale.beneficiary());
        _;
    }

    function test_GivenIsDeductiblesPendingFalse() external whenTheCallerIsABeneficiary {
        // it reverts
        vm.expectRevert(Errors.Forbidden.selector);
        presale.acceptDeductibles();
    }

    function test_GivenIsDeductiblesPendingTrue() external whenDeductiblesIsNotPending whenTheCallerIsABeneficiary {
        (, uint256 pendingAmountBefore,) = presale.deductibles();

        // it emits an event
        vm.expectEmit(true, true, true, true);
        emit VestPresale.DeductiblesUpdateAccepted(pendingAmountBefore);

        presale.acceptDeductibles();

        (uint256 amount, uint256 pendingAmount, bool isPending) = presale.deductibles();

        // it sets deductibles amount to the pending one
        assertEq(amount, pendingAmountBefore);

        // it zeroes deductibles pending amount
        assertEq(pendingAmount, 0);

        // it sets is deductibles pending to false
        assertEq(isPending, false);
    }
}
