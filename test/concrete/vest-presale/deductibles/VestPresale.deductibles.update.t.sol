// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Deductibles } from "src/types/Deductibles.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestTest } from "test/VestTest.sol";

contract Presale_updateDeductibles is VestTest {
    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenTheCallerIsNotAManager() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        presale.updateDeductibles(1 ether);
    }

    function test_WhenTheCallerIsAManager() external {
        vm.startPrank(presale.manager());

        // it emits an event
        vm.expectEmit(true, true, true, true);
        emit VestPresale.DeductiblesUpdateRequested(1 ether);

        presale.updateDeductibles(1 ether);

        (, uint256 pendingAmount, bool isPending) = presale.deductibles();

        // it updates deductibles pending amount
        assertEq(pendingAmount, 1 ether);

        // it sets is deductibles pending to true
        assertEq(isPending, true);
    }
}
