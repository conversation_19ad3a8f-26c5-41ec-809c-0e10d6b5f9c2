// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_updateRoundWhitelist
├── when the caller is not a manager
│   └── it reverts
└── when the caller is a manager
    ├── given the round does not exist
    │   └── it reverts
    └── given the round exists
        ├── when round unlocked
        │   ├── it updates whitelistRoot
        │   ├── it updates proofsUri
        │   └── it emits RoundUpdated event with round id
        └── when round locked
            ├── it updates whitelistRoot
            ├── it updates proofsUri
            └── it emits RoundUpdated event with round id
