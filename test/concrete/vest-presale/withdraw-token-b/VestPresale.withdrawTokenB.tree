// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_withdrawTokenB
├── given listingTimestamp is zero and claimbackPeriod is not zero
│   └── it reverts because all users have the right to refund to prevent griefing
├── given block timestamp is before or at listingTimestamp plus claimbackPeriod
│   └── it reverts because can withdraw only after the claimbackPeriod
├── given liquidityB is zero
│   └── it reverts because there is no tokens to withdraw
├── given liquidityB is lower or equal than claimed back plus deductibles
│   └── it reverts because there is not enough tokens to withdraw
├── given emergency claimbacks are enabled
│   └── it reverts
├── given project withdrawable is lower than the fee
│   ├── it transfers all withdrawable tokens to the fee collector
│   ├── it transfers nothing to the beneficiary
│   ├── it zeros tokenB liquidity
│   ├── it not received fee by fee collector equals percent to receive - received amount
│   └── it emits WithdrawnB event
├── given project withdrawable is equal to the fee
│   ├── it transfers all withdrawable tokens to the fee collector
│   ├── it transfers nothing to the beneficiary
│   ├── it zeros tokenB liquidity
│   ├── it not received fee by fee collector equals 0
│   └── it emits WithdrawnB event
└── given project withdrawable is higher than the fee
    ├── it transfers fee to the fee collector
    ├── it transfers withdrawable minus fee to the beneficiary
    ├── it zeros tokenB liquidity
    ├── it zeros claimedBackTokenB
    ├── it not received fee by fee collector equals 0
    └── it emits WithdrawnB event
