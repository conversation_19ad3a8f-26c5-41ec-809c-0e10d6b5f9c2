// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { Presale } from "src/types/Configuration.sol";
import { Round } from "src/types/Round.sol";
import { Errors } from "src/libraries/Errors.sol";
import { RoundState } from "src/types/Round.sol";
import { VestTest, Configuration } from "test/VestTest.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Participants } from "../../../utils/Participants.sol";
import { Uint256Helper } from "../../../utils/Uint256Helper.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";

contract Presale_withdrawTokenB is VestTest {
    using Participants for Participants.Collection;
    using Uint256Helper for *;

    uint256 internal roundId = 1;

    function setUp() public override {
        super.setUp();
    }

    modifier whenNewRound(uint256 listingTimestamp, uint16 tokenBNumerator) {
        vm.startPrank(controller.owner());

        Configuration memory configuration = composeConfiguration();

        configuration.presale.claimbackPeriod = 7 days;
        configuration.presale.listingTimestamp = listingTimestamp;

        configuration.presale.fees = Presale.Fees({
            tokenANumerator: fees.tokenANumerator,
            tokenADenominator: fees.tokenADenominator,
            tokenBNumerator: tokenBNumerator,
            tokenBDenominator: 100
        });

        uint256 timestamp = vm.unixTime();

        Round[] memory rounds = new Round[](1);
        participants.clear(1);

        // tge: 10%
        participants.add(
            roundId,
            alice,
            0.1e18.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );

        // tge: 0%, price: 0
        participants.add(roundId, bob, 0, 200 ether.decimals(decimalsA), 7 days, 0, 100, 0, 0, 1, 10, 1 days);

        // tge: 0%, cliff: 1 day
        participants.add(
            roundId,
            carol,
            0.1e18.decimals(decimalsB),
            200 ether.decimals(decimalsA),
            7 days,
            0,
            100,
            1 days,
            0,
            1,
            10,
            1 days
        );

        rounds[0] = Round({
            name: "ROUND I",
            startTimestamp: timestamp,
            // e.g. "+ 24 hours" for normal round
            // or "* 2" for KOLs - they bought tokens outside of the protocol and they can claim whenever they want
            endTimestamp: timestamp + 24 hours,
            whitelistRoot: participants.getWhitelistRoot(1),
            proofsUri: "",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: ""
        });

        fixture(configuration, rounds);

        vm.stopPrank();
        _;
    }

    function test_GivenListingTimestampIsZeroAndClaimbackPeriodIsNotZero() external whenNewRound(0, 10) {
        // it reverts because all users have the right to claimback to prevent griefing
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();
    }

    function test_GivenBlockTimestampIsBeforeOrAtListingTimestampPlusClaimbackPeriod()
        external
        whenNewRound(vm.unixTime() + 24 hours, 10)
    {
        uint256 claimbackTimestamp = presale.getListingTimestamp() + presale.claimbackPeriod();

        vm.warp(claimbackTimestamp - 1);

        // it reverts because can withdraw only after the claimbackPeriod
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();

        vm.warp(claimbackTimestamp);

        // it reverts because can withdraw only after the claimbackPeriod
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();
    }

    function test_GivenLiquidityBIsZero() external whenNewRound(vm.unixTime() + 24 hours, 10) {
        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod() + 1);

        // it reverts because there is no tokens to withdraw
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();
    }

    function test_GivenLiquidityBIsLowerOrEqualThanClaimedBackPlusDeductibles()
        external
        whenNewRound(0, 10)
        whenNonSaleClaimbackPercentage(100)
    {
        // it reverts because there is not enough tokens to withdraw
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();
    }

    modifier whenNonSaleClaimbackPercentage(uint256 nonSaleClaimbackAmountAPercentage) {
        // it zeros tokenB liquidity
        assertEq(presale.liquidityB(), 0);

        uint256 saleClaimbackMembershipId = fixture_buy(roundId, alice);
        uint256 nonSaleClaimbackMembershipId = fixture_buy(roundId, carol);

        uint256 saleClaimbackUsage = membership.getUsage(saleClaimbackMembershipId).max;
        uint256 nonSaleClaimbackAmountA =
            membership.getUsage(nonSaleClaimbackMembershipId).max * nonSaleClaimbackAmountAPercentage / 100;

        IVestMembership.Attributes memory saleClaimbackAttributes = membership.getAttributes(saleClaimbackMembershipId);
        IVestMembership.Attributes memory nonSaleClaimbackAttributes =
            membership.getAttributes(nonSaleClaimbackMembershipId);

        uint256 saleClaimbackUsageAmountB = (saleClaimbackUsage * saleClaimbackAttributes.price) / 10 ** decimalsA;
        uint256 nonSaleClaimbackAmountB = (nonSaleClaimbackAmountA * nonSaleClaimbackAttributes.price) / 10 ** decimalsA;

        assert(presale.liquidityB() > 0);
        uint256 liquidityBBeforeSaleRefund = presale.liquidityB();

        assert(presale.getRoundState(roundId) == RoundState.SALE);

        fixture_claimback(saleClaimbackMembershipId, saleClaimbackUsage);

        assertEq(presale.liquidityB(), liquidityBBeforeSaleRefund - saleClaimbackUsageAmountB);

        uint256 endTimestamp = presale.getRound(roundId).endTimestamp;
        vm.warp(endTimestamp + 1);

        assert(presale.getRoundState(roundId) != RoundState.SALE);

        uint256 claimedBackTokenBBeforeNonSaleWithdraw = presale.claimedBackTokenB();
        assertEq(claimedBackTokenBBeforeNonSaleWithdraw, 0);

        uint256 liquidityBBeforeNonSaleWithdraw = presale.liquidityB();

        fixture_claimback(nonSaleClaimbackMembershipId, nonSaleClaimbackAmountA);

        assertEq(presale.liquidityB(), liquidityBBeforeNonSaleWithdraw);
        assertEq(presale.claimedBackTokenB(), claimedBackTokenBBeforeNonSaleWithdraw + nonSaleClaimbackAmountB);

        _;
    }

    function test_GivenLiquidityBOrProjectWithdrawableIsZero()
        external
        whenNewRound(vm.unixTime() + 24 hours, 10)
        whenNonSaleClaimbackPercentage(100)
    {
        assertEq(presale.liquidityB() - presale.claimedBackTokenB(), 0);

        // it reverts because there is no tokens to withdraw
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();
    }

    function test_GivenEmergencyClaimbacksAreEnabled() external whenNewRound(vm.unixTime() + 24 hours, 10) {
        fixture_enableEmergencyClaimbacks();

        assertEq(presale.areEmergencyClaimbacksEnabled(), Boolean.TRUE);

        // it reverts
        vm.expectRevert(Errors.Forbidden.selector);

        presale.withdrawTokenB();
    }

    /// @notice 95% refunds after sale, 10% fee => 5% to the fee collector
    function test_GivenProjectWithdrawableIsLowerThanTheFee()
        external
        whenNewRound(vm.unixTime() + 24 hours, 10)
        whenNonSaleClaimbackPercentage(95)
    {
        uint256 liquidityB = presale.liquidityB();
        uint256 withdrawable = liquidityB - presale.claimedBackTokenB();

        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod() + 1);

        Presale.Fees memory fees = presale.getFees();
        uint256 fee = (liquidityB * fees.tokenBNumerator) / fees.tokenBDenominator;

        assert(withdrawable < fee);

        // it emits WithdrawnB event
        vm.expectEmit(true, false, false, true);
        emit VestPresale.WithdrawnB(withdrawable);

        uint256 feeCollectorBalance = tokenB.balanceOf(presale.getFeeCollector());
        uint256 beneficiaryBalance = tokenB.balanceOf(presale.beneficiary());

        presale.withdrawTokenB();

        // it zeros tokenB liquidity
        assertEq(presale.liquidityB(), 0);

        // it zeros claimedBackTokenB
        assertEq(presale.claimedBackTokenB(), 0);

        // it not received fee by fee collector equals percent to receive - received amount
        assertEq(presale.amountOfNotReceivedFeeByFeeCollector(), fee - withdrawable);

        // it transfers all withdrawable tokens to the fee collector
        assertEq(tokenB.balanceOf(presale.getFeeCollector()), feeCollectorBalance + withdrawable);
        // it transfers nothing to the beneficiary
        assertEq(tokenB.balanceOf(presale.beneficiary()), beneficiaryBalance);
    }

    /// @notice 50% refunds after sale, 50% fee => 50% to the fee collector
    function test_GivenProjectWithdrawableIsEqualToTheFee()
        external
        whenNewRound(vm.unixTime() + 24 hours, 50)
        whenNonSaleClaimbackPercentage(50)
    {
        uint256 liquidityB = presale.liquidityB();
        uint256 withdrawable = liquidityB - presale.claimedBackTokenB();

        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod() + 1);

        Presale.Fees memory fees = presale.getFees();
        uint256 fee = (liquidityB * fees.tokenBNumerator) / fees.tokenBDenominator;

        assert(withdrawable == fee);

        // it emits WithdrawnB event
        vm.expectEmit(true, false, false, true);
        emit VestPresale.WithdrawnB(withdrawable);

        uint256 feeCollectorBalance = tokenB.balanceOf(presale.getFeeCollector());
        uint256 beneficiaryBalance = tokenB.balanceOf(presale.beneficiary());

        presale.withdrawTokenB();

        // it zeros tokenB liquidity
        assertEq(presale.liquidityB(), 0);

        // it not received fee by fee collector equals 0
        assertEq(presale.amountOfNotReceivedFeeByFeeCollector(), 0);

        // it zeros claimedBackTokenB
        assertEq(presale.claimedBackTokenB(), 0);

        // it transfers all withdrawable tokens to the fee collector
        assertEq(tokenB.balanceOf(presale.getFeeCollector()), feeCollectorBalance + withdrawable);
        // it transfers nothing to the beneficiary
        assertEq(tokenB.balanceOf(presale.beneficiary()), beneficiaryBalance);
    }

    /// @notice 10% refunds after sale, 10% fee => 10% to the fee collector, 100% - 10% (refunds) - 10% (fee) = 80% to the beneficiary
    function test_GivenProjectWithdrawableIsHigherThanTheFee()
        external
        whenNewRound(vm.unixTime() + 24 hours, 10)
        whenNonSaleClaimbackPercentage(10)
    {
        uint256 liquidityB = presale.liquidityB();
        uint256 withdrawable = liquidityB - presale.claimedBackTokenB();

        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod() + 1);

        Presale.Fees memory fees = presale.getFees();
        uint256 fee = (liquidityB * fees.tokenBNumerator) / fees.tokenBDenominator;

        assert(withdrawable > fee);

        // it emits WithdrawnB event
        vm.expectEmit(true, false, false, true);
        emit VestPresale.WithdrawnB(withdrawable);

        uint256 feeCollectorBalance = tokenB.balanceOf(presale.getFeeCollector());
        uint256 beneficiaryBalance = tokenB.balanceOf(presale.beneficiary());

        presale.withdrawTokenB();

        // it zeros tokenB liquidity
        assertEq(presale.liquidityB(), 0);

        // it zeros claimedBackTokenB
        assertEq(presale.claimedBackTokenB(), 0);

        // it not received fee by fee collector equals 0
        assertEq(presale.amountOfNotReceivedFeeByFeeCollector(), 0);

        // it transfers all withdrawable tokens to the fee collector
        assertEq(tokenB.balanceOf(presale.getFeeCollector()), feeCollectorBalance + fee);
        // it transfers the rest to the beneficiary
        assertEq(tokenB.balanceOf(presale.beneficiary()), beneficiaryBalance + withdrawable - fee);
    }
}
