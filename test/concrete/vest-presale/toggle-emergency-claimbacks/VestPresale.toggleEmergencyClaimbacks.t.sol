// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestTest } from "test/VestTest.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { VestPresale } from "src/VestPresale.sol";

contract Presale_toggleEmergencyClaimbacks is VestTest {
    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenTheCallerIsNotAManager() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        presale.toggleEmergencyClaimbacks();
    }

    function test_WhenTheCallerIsAManager() external {
        vm.startPrank(manager);

        uint8 wereEnabled = presale.areEmergencyClaimbacksEnabled();
        uint8 expected = wereEnabled == Boolean.TRUE ? Boolean.FALSE : Boolean.TRUE;

        // it emits EmergencyClaimbacksUpdated event
        vm.expectEmit(false, false, false, true);
        emit VestPresale.EmergencyClaimbacksUpdated(expected);

        // it toggles are emergency claimbacks enabled
        presale.toggleEmergencyClaimbacks();

        assertEq(presale.areEmergencyClaimbacksEnabled(), expected);

        vm.stopPrank();
    }
}
