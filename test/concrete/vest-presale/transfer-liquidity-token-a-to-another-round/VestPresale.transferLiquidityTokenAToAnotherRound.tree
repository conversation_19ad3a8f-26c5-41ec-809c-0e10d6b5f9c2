VestPresale_transferLiquidityTokenAToAnotherRound
├── when not manager try to transfer liquidity
│   └── it reverts
├── when given roundIdFrom does not exists
│   └── it reverts
├── when given roundIdTo does not exists
│   └── it reverts
├── when amount is higher than liquidityA in the given round
│   └── it reverts
└── when liquidityA is transferred by manager
    ├── it emits
    ├── it liquidity is reduced in roundIdFrom
    └── it liquidity is increased in roundIdTo