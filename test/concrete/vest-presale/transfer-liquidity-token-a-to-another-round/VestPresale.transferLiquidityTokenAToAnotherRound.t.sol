// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestPresale } from "src/VestPresale.sol";
import { VestTest } from "test/VestTest.sol";
import { Errors } from "src/libraries/Errors.sol";
import { RoundLiquidityA } from "src/types/Round.sol";

contract VestPresale_transferLiquidityTokenAToAnotherRound is VestTest {
    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenNotManagerTryToTransferLiquidity() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        presale.transferLiquidityTokenAToAnotherRound(1, 2, 1 ether);
    }

    function test_WhenGivenRoundIdFromDoesNotExists() external {
        vm.prank(manager);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, 4));
        presale.transferLiquidityTokenAToAnotherRound(4, 2, 1 ether);
    }

    function test_WhenGivenRoundIdToDoesNotExists() external {
        vm.prank(manager);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, 4));
        presale.transferLiquidityTokenAToAnotherRound(1, 4, 1 ether);
    }

    function test_WhenAmountIsHigherThanLiquidityAInTheGivenRound() external {
        vm.prank(manager);

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.transferLiquidityTokenAToAnotherRound(1, 2, 500 ether + 1);
    }

    function test_WhenLiquidityAIsTransferredByManager() external {
        (uint256 liquidityARound1Before,) = presale.roundLiquidityA(1);
        (uint256 liquidityARound2Before,) = presale.roundLiquidityA(2);

        vm.prank(manager);

        // it emits
        vm.expectEmit(true, true, true, true);
        emit VestPresale.LiquidityATransferredToAnotherRound(1, 2, 500 ether);

        presale.transferLiquidityTokenAToAnotherRound(1, 2, 500 ether);

        (uint256 liquidityARound1After,) = presale.roundLiquidityA(1);
        (uint256 liquidityARound2After,) = presale.roundLiquidityA(2);

        // it liquidity is reduced in roundIdFrom
        assertEq(liquidityARound1Before - 500 ether, liquidityARound1After);

        // it liquidity is increased in roundIdTo
        assertEq(liquidityARound2Before + 500 ether, liquidityARound2After);
    }
}
