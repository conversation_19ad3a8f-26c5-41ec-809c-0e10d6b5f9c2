// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Round } from "src/types/Round.sol";
import { VestTest } from "test/VestTest.sol";

contract Presale_removeRound is VestTest {
    uint256 roundId;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenTheCallerIsNotAManager() external {
        (uint256[] memory ids,,) = presale.getRounds();

        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.removeRound(ids[0]);
    }

    modifier whenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_GivenTheRoundDoesNotExist() external whenTheCallerIsAManager {
        (uint256[] memory ids,,) = presale.getRounds();

        roundId = ids[ids.length - 1] + 1;

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, roundId));

        presale.removeRound(roundId);
    }

    modifier givenTheRoundExists() {
        (uint256[] memory ids,,) = presale.getRounds();

        roundId = ids[0];
        _;
    }

    function test_WhenRoundIsLocked() external whenTheCallerIsAManager givenTheRoundExists {
        Round memory round = presale.getRound(roundId);

        vm.warp(round.startTimestamp - 1 hours);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundIsLocked.selector, roundId));

        presale.removeRound(roundId);
    }

    function test_WhenRoundIsNotLocked() external whenTheCallerIsAManager givenTheRoundExists {
        Round memory round = presale.getRound(roundId);

        vm.warp(round.startTimestamp - 1 hours - 1);

        // it emits RoundUpdated event with roundId
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(roundId);

        // it removes the round
        presale.removeRound(roundId);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, roundId));

        presale.getRound(roundId);
    }
}
