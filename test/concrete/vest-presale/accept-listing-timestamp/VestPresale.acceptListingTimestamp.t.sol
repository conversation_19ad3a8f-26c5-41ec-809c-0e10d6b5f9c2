// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Configuration } from "test/VestTest.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestTest } from "test/VestTest.sol";

contract VestPresale_acceptListingTimestamp is VestTest {
    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = block.timestamp + 10 days;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration);
    }

    modifier whenIsPendingTimestamp() {
        vm.prank(manager);
        presale.updateListingTimestamp(vm.unixTime() * 2);
        _;
    }

    function test_WhenNotBeneficiaryTryingToAcceptListingTimestamp() external whenIsPendingTimestamp {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.acceptListingTimestamp();
    }

    function test_WhenPendingTimestampEqualsZero() external {
        // it reverts
        vm.expectRevert(Errors.Forbidden.selector);

        vm.prank(beneficiary);
        presale.acceptListingTimestamp();
    }

    function test_WhenListingTimestampIsAccepted() external whenIsPendingTimestamp {
        (, uint256 pendingListingTimestampBefore) = presale.pendingTimestamps();

        // it emits
        vm.expectEmit(true, true, true, true);
        emit VestPresale.ListingTimestampUpdated(pendingListingTimestampBefore);

        vm.prank(beneficiary);
        presale.acceptListingTimestamp();

        (, uint256 pendingListingTimestampAfter) = presale.pendingTimestamps();

        // it pending timestamp equals zero
        assertEq(0, pendingListingTimestampAfter);

        // it listing timestamp is updated
        assertEq(pendingListingTimestampBefore, presale.getListingTimestamp());
    }
}
