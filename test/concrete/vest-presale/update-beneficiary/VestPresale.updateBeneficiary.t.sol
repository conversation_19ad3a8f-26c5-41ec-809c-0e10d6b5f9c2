// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";

import { VestTest } from "test/VestTest.sol";

contract Presale_updateBeneficiary is VestTest {
    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenTheCallerIsNotABeneficiary() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        presale.updateBeneficiary(chuck);
    }

    modifier whenTheCallerIsABeneficiary() {
        vm.startPrank(beneficiary);
        _;
    }

    function test_GivenTheValueIsZeroAddress() external whenTheCallerIsABeneficiary {
        // it reverts
        vm.expectRevert(Errors.UnacceptableReference.selector);
        presale.updateBeneficiary(address(0));
    }

    function test_GivenTheValueIsValidAddress() external whenTheCallerIsABeneficiary {
        address value = makeAddr("new-beneficiary");

        // it emits BeneficiaryUpdated event
        vm.expectEmit(true, false, false, false);
        emit VestPresale.BeneficiaryUpdated(value);

        presale.updateBeneficiary(value);

        // it modifies Beneficiary address
        assertEq(value, presale.beneficiary(), "it modifies beneficiary address");
    }
}
