// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_updateTgeTimestamp
├── when the caller is not a manager
│   └── it reverts
└── when the caller is a manager
    ├── when new timestamp in the past
    │   └── it reverts
    ├── when new timestamp in the future
    │   ├── when block timestamp at or after tgeTimestamp
    │   │   └── it reverts
    │   ├── when listingTimestamp is equals to zero
    │   │   ├── it modifies state
    │   │   └── it emits TgeTimestampUpdated
    │   └── when listingTimestamp is non zero
    │       ├── when new timestamp in less than listingTimestamp
    │       │   ├── it modifies state
    │       │   └── it emits TgeTimestampUpdated
    │       └── when new timestamp in greater than listingTimestamp
    │           └── it reverts
    └── when new timestamp is greater than 30 days in the future
        ├── it emits
        ├── it tge timestamp is not updated
        └── it pending tge timestamp added
