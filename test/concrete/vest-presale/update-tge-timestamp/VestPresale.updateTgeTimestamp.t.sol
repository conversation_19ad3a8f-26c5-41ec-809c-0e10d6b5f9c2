// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";

import { VestTest, Configuration } from "test/VestTest.sol";

contract Presale_updateTgeTimestamp is VestTest {
    uint256 timestamp;

    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenTheCallerIsNotAManager() external {
        vm.prank(chuck);
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.updateTgeTimestamp(0);
    }

    modifier whenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_WhenNewTimestampInThePast() external whenTheCallerIsAManager {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateTgeTimestamp(block.timestamp - 1);
    }

    modifier whenNewTimestampInTheFuture() {
        timestamp = block.timestamp + 1;
        _;
    }

    function test_WhenBlockTimestampAtOrAfterTgeTimestamp()
        external
        whenTheCallerIsAManager
        whenNewTimestampInTheFuture
    {
        presale.updateTgeTimestamp(timestamp);

        vm.warp(timestamp);

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateTgeTimestamp(timestamp * 2);
    }

    modifier whenListingTimestampIsZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.listingTimestamp = 0;

        presale = fixture(configuration);
        _;
    }

    function test_WhenListingTimestampIsEqualsToZero()
        external
        whenListingTimestampIsZero
        whenTheCallerIsAManager
        whenNewTimestampInTheFuture
    {
        // it emits TgeTimestampUpdated
        vm.expectEmit(false, false, false, true);
        emit VestPresale.TgeTimestampUpdated(timestamp);

        presale.updateTgeTimestamp(timestamp);

        // it modifies state
        assertEq(presale.getTgeTimestamp(), timestamp);
    }

    modifier whenListingTimestampIsNonZero() {
        _;
    }

    function test_WhenNewTimestampInLessThanListingTimestamp()
        external
        whenTheCallerIsAManager
        whenNewTimestampInTheFuture
        whenListingTimestampIsNonZero
    {
        timestamp = presale.getListingTimestamp() - 1;

        // it emits TgeTimestampUpdated
        vm.expectEmit(false, false, false, true);
        emit VestPresale.TgeTimestampUpdated(timestamp);

        presale.updateTgeTimestamp(timestamp);

        // it modifies state
        assertEq(presale.getTgeTimestamp(), timestamp);
    }

    function test_WhenNewTimestampInGreaterThanListingTimestamp()
        external
        whenTheCallerIsAManager
        whenNewTimestampInTheFuture
        whenListingTimestampIsNonZero
    {
        timestamp = presale.getListingTimestamp() + 1;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateTgeTimestamp(timestamp);
    }

    function test_WhenNewTimestampIsGreaterThan30DaysInTheFuture() external whenTheCallerIsAManager {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration);

        // it emits
        vm.expectEmit(true, true, true, true);
        emit VestPresale.UpdateForTgeTimestampRequested(block.timestamp + 31 days);

        vm.prank(manager);
        presale.updateTgeTimestamp(block.timestamp + 31 days);

        // it tge timestamp is not updated
        assertEq(presale.getTgeTimestamp(), 0);

        // it pending tge timestamp added
        (uint256 pendingTgeTimestamp,) = presale.pendingTimestamps();
        assertEq(pendingTgeTimestamp, block.timestamp + 31 days);
    }
}
