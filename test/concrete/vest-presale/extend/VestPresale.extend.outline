Presale_extend
  given a round is in vesting state
    it reverts
  given a round is in sale state
    when an attacker uses a Membership they do not own
      it reverts
    when the caller buys with the Membership they own and that is bought out
      it reverts
    when the caller buys with the Membership they own and that is not bought out
      given the roundLiquidityA is zero
        it reverts
      given the Membership has zero price
        it works without an allowance for tokenB
        it modifies their Membership to reflect that purchase
        it emits DynamicIdNFTUpdated
      given the Membership has non-zero price
        given the caller did not give an allowance
          it reverts
        given the caller does not have enough tokenB
          it reverts
        when the caller buys more than their Membership allows for
          it buys less tokenA than they asked for
          it pays with tokenB
          it modifies their Membership to reflect their purchase
          it emits DynamicIdNFTUpdated
        when the caller buys more than round tokenA liquidity
          it buys less tokenA than they asked for
          it pays with tokenB
          it modifies their Membership to reflect their purchase
          it emits DynamicIdNFTUpdated
        when the caller buys more or equal than round tokenA liquidity and got sold check is false
          it buys the entire liquidity
          it got sold check is true
        when the caller buys more or equal than round tokenA liquidity and got sold check is true
          it buys the entire liquidity
          it got sold check has not changed
        when the user calls extend multiple times
          it works every time
        when a user sends native coin
          given the caller sends not have enough coins
            it reverts
          when the caller send more coins than their Membership allows for
            it buys less tokenA than they asked for
            it pays with tokenB and send back excess
            it modifies their Membership to reflect their purchase
            it emits DynamicIdNFTUpdated
          when the caller sends more coins than round tokenA liquidity
            it buys less tokenA than they asked for
            it pays with tokenB and send back excess
            it modifies their Membership to reflect their purchase
            it emits DynamicIdNFTUpdated
          when the user sends coins multiple times
            it works every time
      given a user is a smart contract
        it buys tokenA
        it pays with tokenB
        it modifies their Membership to reflect their purchase
        it emits DynamicIdNFTUpdated
