// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestTest, Configuration } from "test/VestTest.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Round, RoundState } from "src/types/Round.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "test/utils/Participants.sol";
import { ERC721AcceptingReceiver } from "test/samples/ERC721AcceptingReceiver.sol";
import { ERC721DynamicIds } from "src/utils/ERC721DynamicIds.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";
import { WETHSample } from "test/samples/WETHSample.sol";

contract Presale_extend is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;

    address eRC721AcceptingReceiver = address(new ERC721AcceptingReceiver());

    uint256 membershipId;
    uint256 internal roundId = 1;

    using Participants for Participants.Collection;

    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();

        configuration.presale.tokenB = tokenB = new WETHSample();

        fixture(configuration);

        participants.add(
            1,
            eRC721AcceptingReceiver,
            0.1e18.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );

        tokenB.safeTransfer(
            eRC721AcceptingReceiver, (100 ether.decimals(decimalsA) * 0.1e18.decimals(decimalsB)) / 10 ** decimalsA
        );

        bytes32 whitelistRoot = participants.getWhitelistRoot(1);

        vm.prank(manager);
        presale.updateRoundWhitelist(roundId, whitelistRoot, "");
    }

    function test_GivenARoundIsInVestingState() external {
        membershipId = fixture_buy(roundId, alice);

        vm.warp(presale.getRound(roundId).endTimestamp * 10);

        // it reverts
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.RoundStateMismatch.selector, roundId, RoundState.VESTING, RoundState.SALE
            )
        );

        // TODO update this in other tests in this file
        presale.extend(membershipId, 1);
    }

    modifier givenARoundIsInSaleState() {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.startTimestamp);
        _;
    }

    function test_WhenAnAttackerUsesAMembershipTheyDoNotOwn() external givenARoundIsInSaleState {
        membershipId = fixture_buy(roundId, alice);

        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, chuck));
        presale.extend(membershipId, 1);
    }

    function test_WhenTheCallerBuysWithTheMembershipTheyOwnAndThatIsBoughtOut() external givenARoundIsInSaleState {
        membershipId = fixture_buy(roundId, alice);

        vm.startPrank(alice);

        // it reverts
        vm.expectRevert(); // there is no custom error
        presale.extend(membershipId, 1);
    }

    modifier whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(address account) {
        Participants.Participant memory participant = participants.get(roundId, account);

        membershipId = fixture_buy(roundId, account, participant.allocation / 2);

        vm.startPrank(account);
        _;
    }

    function test_GivenTheRoundLiquidityAIsZero()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
    {
        vm.stopPrank();

        // reset roundLiquidityA to zero
        vm.startPrank(beneficiary);

        (uint256 liquidityA,) = presale.roundLiquidityA(roundId);
        presale.withdrawTokenA(roundId, liquidityA);

        vm.startPrank(alice);

        // it reverts
        vm.expectRevert(
            abi.encodeWithSelector(VestPresale.RoundStateMismatch.selector, 1, RoundState.VESTING, RoundState.SALE)
        );
        presale.extend(membershipId, 1);
    }

    function test_GivenTheMembershipHasZeroPrice()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(bob)
    {
        Participants.Participant memory participant = participants.get(roundId, bob);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it works without an allowance for tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect that purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    modifier givenTheMembershipHasNon_zeroPrice() {
        _;
    }

    function test_GivenTheCallerDidNotGiveAnAllowance()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientAllowance)
        presale.extend(membershipId, amountA);
    }

    function test_GivenTheCallerDoesNotHaveEnoughTokenB()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(alice, amountB / 2);

        vm.startPrank(alice);

        tokenB.forceApprove(address(presale), amountB);

        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientBalance)
        presale.extend(membershipId, amountA);
    }

    function test_WhenTheCallerBuysMoreThanTheirMembershipAllowsFor()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 amountB = (amountA * 2 * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(alice, amountB / 2);

        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it buys less tokenA than they asked for
        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA * 2);

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    function test_WhenTheCallerBuysMoreThanRoundTokenALiquidity()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.startPrank(beneficiary);

        (uint256 liquidityA,) = presale.roundLiquidityA(roundId);
        presale.withdrawTokenA(roundId, liquidityA);

        vm.stopPrank();
        tokenA.forceApprove(address(presale), amountA / 2);
        presale.depositTokenA(roundId, amountA / 2);

        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 expectedBoughtAmount = amountA + (amountA / 2);
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: expectedBoughtAmount, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        // it buys less tokenA than they asked for
        assertEq(usage.max, expectedBoughtAmount, "usage.max equals bought amount");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    function test_WhenTheCallerBuysMoreOrEqualThanRoundTokenALiquidityAndGotSoldCheckIsFalse()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        // it buys the entire liquidity
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.startPrank(beneficiary);

        (uint256 liquidityA,) = presale.roundLiquidityA(roundId);
        presale.withdrawTokenA(roundId, liquidityA);

        vm.stopPrank();

        (, bool gotSoldBefore) = presale.roundLiquidityA(roundId);
        assertFalse(gotSoldBefore, "gotSold is false");

        tokenA.forceApprove(address(presale), amountA / 2);
        presale.depositTokenA(roundId, amountA / 2);

        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 expectedBoughtAmount = amountA + (amountA / 2);
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: expectedBoughtAmount, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        // it buys less tokenA than they asked for
        assertEq(usage.max, expectedBoughtAmount, "usage.max equals bought amount");
        assertEq(usage.current, 0, "usage.current equals claimed amount");

        // it got sold check is true
        (, bool gotSoldAfter) = presale.roundLiquidityA(roundId);

        assertTrue(gotSoldAfter, "gotSold is true");
    }

    function test_WhenTheCallerBuysMoreOrEqualThanRoundTokenALiquidityAndGotSoldCheckIsTrue()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        // it buys the entire liquidity
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.startPrank(beneficiary);

        (uint256 liquidityA,) = presale.roundLiquidityA(roundId);
        presale.withdrawTokenA(roundId, liquidityA);

        vm.stopPrank();

        (, bool gotSoldBefore) = presale.roundLiquidityA(roundId);
        assertFalse(gotSoldBefore, "gotSold is false");

        tokenA.forceApprove(address(presale), amountA / 2);
        presale.depositTokenA(roundId, amountA / 2);

        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 expectedBoughtAmount = amountA + (amountA / 2);
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: expectedBoughtAmount, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA);

        vm.stopPrank();

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        // it buys less tokenA than they asked for
        assertEq(usage.max, expectedBoughtAmount, "usage.max equals bought amount");
        assertEq(usage.current, 0, "usage.current equals claimed amount");

        // it got sold check is true
        (, bool gotSoldAfter) = presale.roundLiquidityA(roundId);

        assertTrue(gotSoldAfter, "gotSold is true");

        fixture_claimback(membershipId);

        (uint256 liquidityAAfterClaimback, bool gotSoldAfterClaimback) = presale.roundLiquidityA(roundId);

        assert(liquidityAAfterClaimback > 0);

        // it got sold check has not changed
        assertTrue(gotSoldAfterClaimback, "gotSoldAfter is true");
    }

    function test_WhenTheUserCallsExtendMultipleTimes(uint32 steps)
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
    {
        vm.assume(steps >= 2 && steps <= 10);

        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 liquidityB = presale.liquidityB();

        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(alice, amountB);

        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 rest = amountA;
        uint256 amount = amountA / steps;
        // it works every time
        for (uint256 i = 1; i <= steps - 1; i++) {
            (membershipId) = presale.extend(membershipId, amount);

            rest -= amount;
        }

        if (rest != 0) membershipId = presale.extend(membershipId, rest);

        assertEq(presale.liquidityB(), liquidityB + amountB, "liquidityB has been increased by amountB");

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(liquidityAAfter, liquidityABefore - amountA, "roundLiquidityA has been reduced by amountA");

        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals amount bought");
        assertEq(usage.current, 0, "usage.current equals zero");
    }

    modifier whenAUserSendsNativeCoin() {
        _;
    }

    function test_GivenTheCallerSendsNotHaveEnoughCoins()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
        whenAUserSendsNativeCoin
    {
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        deal(alice, amountB);

        vm.startPrank(alice);

        // it reverts
        vm.expectRevert(Errors.Forbidden.selector);
        presale.extend{ value: amountB / 2 }(membershipId, amountA);
    }

    function test_WhenTheCallerSendMoreCoinsThanTheirMembershipAllowsFor()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
        whenAUserSendsNativeCoin
    {
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 amountB = (amountA * 2 * participant.price) / 10 ** decimalsA;

        deal(alice, amountB);

        uint256 balance = tokenB.balanceOf(address(presale));

        vm.startPrank(alice);

        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it buys less tokenA than they asked for
        // it pays with tokenB
        membershipId = presale.extend{ value: amountB }(membershipId, amountA);

        assertEq(alice.balance, amountB / 2, "it pays with tokenB and send back excess");

        assertEq(tokenB.balanceOf(address(presale)), balance + amountB / 2, "balance has been increased");

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    function test_WhenTheCallerSendsMoreCoinsThanRoundTokenALiquidity()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
        whenAUserSendsNativeCoin
    {
        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.startPrank(beneficiary);

        (uint256 liquidityA,) = presale.roundLiquidityA(roundId);
        presale.withdrawTokenA(roundId, liquidityA);
        vm.stopPrank();

        tokenA.forceApprove(address(presale), amountA / 2);
        presale.depositTokenA(roundId, amountA / 2);

        deal(alice, amountB);

        uint256 balance = tokenB.balanceOf(address(presale));

        vm.startPrank(alice);

        uint256 expectedBoughtAmount = amountA + (amountA / 2);
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: expectedBoughtAmount, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it pays with tokenB
        membershipId = presale.extend{ value: amountB }(membershipId, amountA);

        assertEq(alice.balance, amountB / 2, "it pays with tokenB and send back excess");

        assertEq(tokenB.balanceOf(address(presale)), balance + amountB / 2, "balance has been increased");

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        // it buys less tokenA than they asked for
        assertEq(usage.max, expectedBoughtAmount, "usage.max equals bought amount");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    function test_WhenTheUserSendsCoinsMultipleTimes(uint32 steps)
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(alice)
        givenTheMembershipHasNon_zeroPrice
        whenAUserSendsNativeCoin
    {
        vm.assume(steps >= 2 && steps <= 10);

        (uint256 liquidityABefore,) = presale.roundLiquidityA(roundId);
        uint256 liquidityB = presale.liquidityB();

        Participants.Participant memory participant = participants.get(roundId, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        deal(alice, amountB);

        vm.startPrank(alice);

        uint256 remainder = amountA;
        uint256 amount = amountA / steps;

        // it works every time
        for (uint256 i = 1; i <= steps - 1; i++) {
            (membershipId) = presale.extend{ value: alice.balance }(membershipId, amount);

            remainder -= amount;
        }

        if (remainder != 0) {
            membershipId = presale.extend{ value: alice.balance }(membershipId, remainder);
        }

        assertEq(presale.liquidityB(), liquidityB + amountB, "liquidityB has been increased by amountB");

        (uint256 liquidityAAfter,) = presale.roundLiquidityA(roundId);
        assertEq(liquidityAAfter, liquidityABefore - amountA, "roundLiquidityA has been reduced by amountA");

        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals amount bought");
        assertEq(usage.current, 0, "usage.current equals zero");
    }

    function test_GivenAUserIsASmartContract()
        external
        givenARoundIsInSaleState
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(eRC721AcceptingReceiver)
    {
        Participants.Participant memory participant = participants.get(roundId, eRC721AcceptingReceiver);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        tokenB.forceApprove(address(presale), (amountA * participant.price) / 10 ** decimalsA);

        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it buys tokenA
        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect that purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }
}
