// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Vest_constructor
├── when tokenA fee is greater than or equal to 100 percent
│   └── it reverts
├── when tokenB fee is greater than or equal to 100 percent
│   └── it reverts
├── when tokenA fee denominator equals 0
│   └── it reverts because division by 0 is not allowed
├── when tokenB fee denominator equals 0
│   └── it reverts because division by 0 is not allowed
└── when fee structure is valid
    ├── it modifies state variables
    ├── it sets the deployer as the owner
    └── it emits FeesUpdated event
