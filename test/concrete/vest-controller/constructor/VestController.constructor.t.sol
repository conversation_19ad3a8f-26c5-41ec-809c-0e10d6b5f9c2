// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Fees } from "src/types/Fees.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestController } from "src/VestController.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_constructor is VestControllerTest {
    Fees fees = Fees({
        tokenANumerator: 0,
        tokenADenominator: 1,
        tokenBNumerator: 10,
        tokenBDenominator: 100,
        nftNumerator: 1_000,
        nftDenominator: 10_000
    });

    function test_WhenTokenAFeeIsGreaterThanOrEqualTo100Percent() external {
        fees.tokenANumerator = fees.tokenADenominator;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestController(presaleDeployer, membershipDeployer, fees);

        fees.tokenANumerator = fees.tokenADenominator + 1;
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestController(presaleDeployer, membershipDeployer, fees);
    }

    function test_WhenTokenBFeeIsGreaterThanOrEqualTo100Percent() external {
        fees.tokenBNumerator = fees.tokenBDenominator;
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestController(presaleDeployer, membershipDeployer, fees);

        fees.tokenANumerator = fees.tokenADenominator + 1;
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestController(presaleDeployer, membershipDeployer, fees);
    }

    function test_WhenTokenAFeeDenominatorEquals0() external {
        fees.tokenADenominator = 0;

        // it reverts because division by 0 is not allowed
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestController(presaleDeployer, membershipDeployer, fees);
    }

    function test_WhenTokenBFeeDenominatorEquals0() external {
        fees.tokenBDenominator = 0;

        // it reverts because division by 0 is not allowed
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestController(presaleDeployer, membershipDeployer, fees);
    }

    function test_WhenFeeStructureIsValid() external {
        address deployer = makeAddr("deployer");
        vm.startPrank(deployer);
        // it emits FeesUpdated event
        vm.expectEmit(false, false, false, true);
        emit VestController.FeesUpdated(fees);

        VestController controller = new VestController(presaleDeployer, membershipDeployer, fees);
        Fees memory fees_ = controller.getFees();
        // it modifies state variables
        assertEq(fees_.tokenANumerator, 0);
        assertEq(fees_.tokenADenominator, 1);
        assertEq(fees_.tokenBNumerator, 10);
        assertEq(fees_.tokenBDenominator, 100);
        assertEq(fees_.nftNumerator, 1_000);
        assertEq(fees_.nftDenominator, 10_000);

        // it sets the deployer as the owner
        assertEq(controller.owner(), deployer);
    }
}
