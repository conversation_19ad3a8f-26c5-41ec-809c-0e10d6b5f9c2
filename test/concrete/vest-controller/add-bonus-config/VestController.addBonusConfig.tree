// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Vest_addBonusConfig
├── when denominator equals 0
│   └── it reverts with InvalidBonusConfiguration
├── when the caller is an attacker
│   └── it reverts with OwnableUnauthorizedAccount
└── when bonus configuration is valid
    ├── it adds bonus to allowed list
    ├── it emits BonusAdded event with correct parameters
    └── it assigns correct bonusId
