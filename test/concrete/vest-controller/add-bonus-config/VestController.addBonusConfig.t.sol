// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";

import { VestController } from "src/VestController.sol";
import { Bonus } from "src/types/Bonus.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_addBonusConfig is VestControllerTest {
    function test_WhenDenominatorEquals0() external {
        // it reverts with InvalidBonusConfiguration
        vm.expectRevert(VestController.InvalidBonusConfiguration.selector);
        controller.addBonusConfig(1, 0);
    }

    function test_WhenBonusConfigurationIsValid() external {
        // it emits BonusAdded event with correct parameters for regular bonus
        vm.expectEmit(true, false, false, true);
        emit VestController.BonusAdded(1, 1, 10);
        
        controller.addBonusConfig(1, 10);

        // it adds bonus to allowed list
        Bonus[] memory bonuses = controller.getAllowedBonuses();
        assertEq(bonuses.length, 1);
        assertEq(bonuses[0].numerator, 1);
        assertEq(bonuses[0].denominator, 10);

        // it assigns correct bonusId (sequential) and allows 100% bonus
        vm.expectEmit(true, false, false, true);
        emit VestController.BonusAdded(2, 10, 10);
        
        controller.addBonusConfig(10, 10);

        // it allows bonus greater than 100%
        vm.expectEmit(true, false, false, true);
        emit VestController.BonusAdded(3, 20, 10);
        
        controller.addBonusConfig(20, 10);
    }

    function test_WhenTheCallerIsAnAttacker() external {
        // it reverts with OwnableUnauthorizedAccount
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, alice));
        vm.prank(alice);
        controller.addBonusConfig(1, 10);
    }
}
