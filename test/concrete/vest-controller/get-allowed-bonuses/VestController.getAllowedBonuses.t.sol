// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestController } from "src/VestController.sol";
import { Bonus } from "src/types/Bonus.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_getAllowedBonuses is VestControllerTest {
    function test_WhenNoBonusesAreSet() external {
        // it returns empty array
        Bonus[] memory bonuses = controller.getAllowedBonuses();
        assertEq(bonuses.length, 0);
    }

    function test_WhenBonusesExist() external {
        // it returns array with all bonuses
        controller.addBonusConfig(1, 10);
        controller.addBonusConfig(2, 20);
        controller.addBonusConfig(3, 30);

        Bonus[] memory bonuses = controller.getAllowedBonuses();
        assertEq(bonuses.length, 3);

        // it returns bonuses in correct order
        assertEq(bonuses[0].numerator, 1);
        assertEq(bonuses[0].denominator, 10);
        assertEq(bonuses[1].numerator, 2);
        assertEq(bonuses[1].denominator, 20);
        assertEq(bonuses[2].numerator, 3);
        assertEq(bonuses[2].denominator, 30);
    }

    function test_WhenBonusIsRemoved() external {
        // Setup initial bonuses
        controller.addBonusConfig(1, 10);
        controller.addBonusConfig(2, 20);
        controller.addBonusConfig(3, 30);

        // Remove middle bonus
        controller.removeBonusConfig(2);

        // it returns array without removed bonus
        Bonus[] memory bonuses = controller.getAllowedBonuses();
        assertEq(bonuses.length, 2);

        // it maintains correct order of remaining bonuses
        assertEq(bonuses[0].numerator, 1);
        assertEq(bonuses[0].denominator, 10);
        assertEq(bonuses[1].numerator, 3);
        assertEq(bonuses[1].denominator, 30);
    }
}
