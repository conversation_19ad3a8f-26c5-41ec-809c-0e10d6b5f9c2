// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Vest_removeBonusConfig
├── when bonus id does not exist
│   └── it reverts with IndexOutOfBounds
├── when not owner tries to remove bonus
│   └── it reverts with OwnableUnauthorizedAccount
└── when bonus exists
    ├── it removes bonus from allowed list
    ├── it emits BonusRemoved event with correct id
    └── it cannot use removed bonus id again
