// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";

import { VestController } from "src/VestController.sol";
import { Bonus } from "src/types/Bonus.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_removeBonusConfig is VestControllerTest {
    function test_WhenBonusIdDoesNotExist() external {
        // it reverts with IndexOutOfBounds
        vm.expectRevert(VestController.IndexOutOfBounds.selector);
        controller.removeBonusConfig(1);

        // Add and remove a bonus, then try to remove it again
        controller.addBonusConfig(1, 10);
        controller.removeBonusConfig(1);
        
        vm.expectRevert(VestController.IndexOutOfBounds.selector);
        controller.removeBonusConfig(1);
    }

    function test_WhenNotOwnerTriesToRemoveBonus() external {
        // Add a bonus first
        controller.addBonusConfig(1, 10);

        // it reverts with OwnableUnauthorizedAccount
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, alice));
        vm.prank(alice);
        controller.removeBonusConfig(1);
    }

    function test_WhenBonusExists() external {
        // Setup initial bonuses
        controller.addBonusConfig(1, 10);
        controller.addBonusConfig(2, 20);
        controller.addBonusConfig(3, 30);

        // it emits BonusRemoved event with correct id
        vm.expectEmit(true, false, false, false);
        emit VestController.BonusRemoved(2);
        
        controller.removeBonusConfig(2);

        // it removes bonus from allowed list
        Bonus[] memory bonuses = controller.getAllowedBonuses();
        assertEq(bonuses.length, 2);
        
        // Verify remaining bonuses
        assertEq(bonuses[0].numerator, 1);
        assertEq(bonuses[0].denominator, 10);
        assertEq(bonuses[1].numerator, 3);
        assertEq(bonuses[1].denominator, 30);

        // it cannot use removed bonus id again
        vm.expectRevert(VestController.IndexOutOfBounds.selector);
        controller.removeBonusConfig(2);
    }
}
