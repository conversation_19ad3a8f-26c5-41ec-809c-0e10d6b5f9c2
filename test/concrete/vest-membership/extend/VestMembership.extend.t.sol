// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";

import { IVestMembership } from "src/VestMembership.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_extend is VestMembershipTest {
    uint256 internal membershipId;

    function setUp() public {
        membership = fixture();

        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);
    }

    function test_GivenTheCallerIsNotAnOwner() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, chuck));
        membership.extend(membershipId, 1 ether);
    }

    modifier givenTheCallerIsAnOwner() {
        _;
    }

    function test_GivenTheMembershipDoesNotExist() external givenTheCallerIsAnOwner {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, 0));
        membership.extend(0, 1 ether);
    }

    function test_GivenTheMembershipExists(uint256 amount) external givenTheCallerIsAnOwner {
        IVestMembership.Usage memory previous = membership.getUsage(membershipId);

        vm.assume(amount <= type(uint256).max - previous.max);

        membershipId = membership.extend(membershipId, amount);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, previous.max + amount, "it increases max usage by value");
    }
}
