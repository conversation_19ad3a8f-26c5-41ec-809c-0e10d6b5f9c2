// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_getStartTimestamp is VestMembershipTest {
    function setUp() public {
        membership = fixture();
    }

    function test_ReturnsValueOfListingTimestampFromScheduler() external {
        uint256 timestamp = 1708124317865;

        vm.mockCall(
            scheduler, abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector), abi.encode(timestamp)
        );

        assertEq(membership.getStartTimestamp(), timestamp, "it returns value listing timestamp from scheduler");
    }
}
