Membership_unlocked
  given a membership does not exist
    it reverts
  given a membership exists
    when tge timestamp equals zero
      it returns zero
    when tge timestamp is non zero
      when block timestamp is before tge timestamp
        it returns zero
      when block timestamp is at tge timestamp
        it returns tge amount
      when block timestamp is after tge timestamp and listing timestamp equals zero
        it returns tge amount
      when listing timestamp is non zero
        when block timestamp is after tge timestamp and is before listing timestamp
          it returns tge amount
        when block timestamp is after listing timestamp
          given cliff duration greater than zero and cliff numerator zero
            when block timestamp during flat cliff
              it returns tge amount
            when block timestamp at flat cliff end
              it returns tge amount plus amount of the first vesting period
            when block timestamp during the first vesting period flat cliff
              it returns tge amount plus amount of the first vesting period
            when block timestamp at the end of the first vesting period flat cliff
              it returns tge amount plus amount of 2 vesting periods
            when block timestamp at the start of the last vesting period
              it returns amount of all vested tokens
            when block timestamp after the last vesting period start
              it returns amount of all vested tokens
          given no cliff
            when block timestamp during the first vesting period no cliff
              it returns tge amount
            when block timestamp at the end of the first vesting period no cliff
              it returns tge amount plus amount of the first vesting period
            when block timestamp during the second vesting period no cliff
              it returns tge amount plus amount of the first vesting period
            when block timestamp at the end of the second vesting period no cliff
              it returns tge amount plus amount of 2 vesting periods
            when block timestamp at the end of the last vesting period without cliff
              it returns amount of all vested tokens
            when block timestamp after the last vesting period end no cliff
              it returns amount of all vested tokens
          given cliff numerator greater than zero
            when block timestamp during cliff
              it returns tge amount
            when block timestamp at cliff end
              it returns tge amount plus cliff unlock
            when block timestamp during the first vesting period
              it returns tge amount plus cliff unlock
            when block timestamp at the end of the first vesting period
              it returns tge amount plus cliff unlock plus amount of the first vesting period
            when block timestamp during the second vesting period
              it returns tge amount plus cliff unlock plus amount of the first vesting period
            when block timestamp at the end of the second vesting period
              it returns tge amount plus cliff unlock plus amount of 2 vesting periods
            when block timestamp at the end of the last vesting period
              it returns amount of all vested tokens
            when block timestamp after the last vesting period end
              it returns amount of all vested tokens
