// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";

import { IVestMembership } from "src/VestMembership.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_unlocked is VestMembershipTest {
    uint256 internal membershipId;

    IVestMembership.Attributes internal attributes;

    uint256 internal tgeTimestamp;
    uint256 internal listingTimestamp;

    function setUp() public {
        membership = fixture();

        tgeTimestamp = vm.unixTime();
        listingTimestamp = vm.unixTime() + (vm.unixTime() % 1000);
    }

    function test_GivenAMembershipDoesNotExist() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, 0));
        membership.unlocked(0);
    }

    modifier givenAMembershipExists() {
        attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 100,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 100 ether, attributes);
        _;
    }

    function test_WhenTgeTimestampEqualsZero() external givenAMembershipExists {
        vm.mockCall(scheduler, abi.encodeWithSelector(IVestPresaleScheduler.getTgeTimestamp.selector), abi.encode(0));

        vm.mockCall(
            scheduler, abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector), abi.encode(0)
        );

        assertEq(membership.unlocked(membershipId), 0, "it returns zero");
    }

    modifier whenTgeTimestampIsNonZero() {
        vm.mockCall(
            scheduler, abi.encodeWithSelector(IVestPresaleScheduler.getTgeTimestamp.selector), abi.encode(tgeTimestamp)
        );

        vm.mockCall(
            scheduler, abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector), abi.encode(0)
        );
        _;
    }

    function test_WhenBlockTimestampIsBeforeTgeTimestamp() external givenAMembershipExists whenTgeTimestampIsNonZero {
        vm.warp(tgeTimestamp - 1);

        assertEq(membership.unlocked(membershipId), 0, "it returns zero");
    }

    function test_WhenBlockTimestampIsAtTgeTimestamp() external givenAMembershipExists whenTgeTimestampIsNonZero {
        vm.warp(tgeTimestamp);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        assertEq(membership.unlocked(membershipId), tge, "it returns tge amount");
    }

    function test_WhenBlockTimestampIsAfterTgeTimestampAndListingTimestampEqualsZero()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
    {
        vm.warp(tgeTimestamp + 1);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        assertEq(membership.unlocked(membershipId), tge, "it returns tge amount");
    }

    modifier whenListingTimestampIsNonZero() {
        vm.mockCall(
            scheduler,
            abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector),
            abi.encode(listingTimestamp)
        );
        _;
    }

    function test_WhenBlockTimestampIsAfterTgeTimestampAndIsBeforeListingTimestamp()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
    {
        vm.warp(listingTimestamp - 1);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        assertEq(membership.unlocked(membershipId), tge, "it returns tge amount");
    }

    modifier whenBlockTimestampIsAfterListingTimestamp() {
        vm.warp(listingTimestamp + 1);
        _;
    }

    modifier givenCliffDurationGreaterThanZeroAndCliffNumeratorZero() {
        attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 10,
            cliffNumerator: 0,
            cliffDenominator: 100,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 1, 0, 100 ether, attributes);
        _;
    }

    function test_WhenBlockTimestampDuringFlatCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffDurationGreaterThanZeroAndCliffNumeratorZero
    {
        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        assertEq(membership.unlocked(membershipId), tge, "it returns tge amount");
    }

    function test_WhenBlockTimestampAtFlatCliffEnd()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffDurationGreaterThanZeroAndCliffNumeratorZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 installment = (attributes.allocation - tge) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + installment,
            "it returns tge amount plus amount of the first vesting period"
        );
    }

    function test_WhenBlockTimestampDuringTheFirstVestingPeriodFlatCliff()
        external
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffDurationGreaterThanZeroAndCliffNumeratorZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration + 1);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 installment = (attributes.allocation - tge) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + installment,
            "it returns tge amount plus amount of the first vesting period"
        );
    }

    function test_WhenBlockTimestampAtTheEndOfTheFirstVestingPeriodFlatCliff()
        external
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffDurationGreaterThanZeroAndCliffNumeratorZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration + attributes.vestingPeriodDuration);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 installment = (attributes.allocation - tge) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + installment * 2,
            "it returns tge amount plus amount of 2 vesting periods"
        );
    }

    function test_WhenBlockTimestampAtTheStartOfTheLastVestingPeriod()
        external
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffDurationGreaterThanZeroAndCliffNumeratorZero
    {
        vm.warp(
            listingTimestamp + attributes.cliffDuration
                + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration)
        );

        assertEq(membership.unlocked(membershipId), attributes.allocation, "it returns amount of all vested tokens");
    }

    function test_WhenBlockTimestampAfterTheLastVestingPeriodStart()
        external
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffDurationGreaterThanZeroAndCliffNumeratorZero
    {
        vm.warp(
            listingTimestamp + attributes.cliffDuration
                + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration) + 1
        );

        assertEq(membership.unlocked(membershipId), attributes.allocation, "it returns amount of all vested tokens");
    }

    modifier givenNoCliff() {
        attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 100,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 2, 0, 100 ether, attributes);
        _;
    }

    function test_WhenBlockTimestampDuringTheFirstVestingPeriodNoCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenNoCliff
    {
        vm.warp(listingTimestamp);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        assertEq(membership.unlocked(membershipId), tge, "it returns tge amount");
    }

    function test_WhenBlockTimestampAtTheEndOfTheFirstVestingPeriodNoCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenNoCliff
    {
        vm.warp(listingTimestamp + attributes.vestingPeriodDuration);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 installment = (attributes.allocation - tge) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + installment,
            "it returns tge amount plus amount of the first vesting period"
        );
    }

    function test_WhenBlockTimestampDuringTheSecondVestingPeriodNoCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenNoCliff
    {
        vm.warp(listingTimestamp + attributes.vestingPeriodDuration + 1);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 installment = (attributes.allocation - tge) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + installment,
            "it returns tge amount plus amount of the first vesting period"
        );
    }

    function test_WhenBlockTimestampAtTheEndOfTheSecondVestingPeriodNoCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenNoCliff
    {
        vm.warp(listingTimestamp + (attributes.vestingPeriodDuration * 2));

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 installment = (attributes.allocation - tge) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + (installment * 2),
            "it returns tge amount plus amount of 2 vesting periods"
        );
    }

    function test_WhenBlockTimestampAtTheEndOfTheLastVestingPeriodWithoutCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenNoCliff
    {
        vm.warp(listingTimestamp + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration));

        assertEq(membership.unlocked(membershipId), attributes.allocation, "it returns amount of all vested tokens");
    }

    function test_WhenBlockTimestampAfterTheLastVestingPeriodEndNoCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenNoCliff
    {
        vm.warp(listingTimestamp + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration) + 1);

        assertEq(membership.unlocked(membershipId), attributes.allocation, "it returns amount of all vested tokens");
    }

    modifier givenCliffNumeratorGreaterThanZero() {
        attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 1 days,
            cliffNumerator: 10,
            cliffDenominator: 100,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(carol, 1, 0, 100 ether, attributes);
        _;
    }

    function test_WhenBlockTimestampDuringCliff()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(listingTimestamp);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        assertEq(membership.unlocked(membershipId), tge, "it returns tge amount");
    }

    function test_WhenBlockTimestampAtCliffEnd()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 unlock = (attributes.allocation * attributes.cliffNumerator) / attributes.cliffDenominator;

        assertEq(membership.unlocked(membershipId), tge + unlock, "it returns tge amount plus cliff unlock");
    }

    function test_WhenBlockTimestampDuringTheFirstVestingPeriod()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration + 1);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 unlock = (attributes.allocation * attributes.cliffNumerator) / attributes.cliffDenominator;

        assertEq(membership.unlocked(membershipId), tge + unlock, "it returns tge amount plus cliff unlock");
    }

    function test_WhenBlockTimestampAtTheEndOfTheFirstVestingPeriod()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration + attributes.vestingPeriodDuration);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 unlock = (attributes.allocation * attributes.cliffNumerator) / attributes.cliffDenominator;
        uint256 installment = (attributes.allocation - tge - unlock) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + unlock + installment,
            "it returns tge amount plus cliff unlock plus amount of the first vesting period"
        );
    }

    function test_WhenBlockTimestampDuringTheSecondVestingPeriod()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration + attributes.vestingPeriodDuration + 1);

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 unlock = (attributes.allocation * attributes.cliffNumerator) / attributes.cliffDenominator;
        uint256 installment = (attributes.allocation - tge - unlock) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + unlock + installment,
            "it returns tge amount plus cliff unlock plus amount of the first vesting period"
        );
    }

    function test_WhenBlockTimestampAtTheEndOfTheSecondVestingPeriod()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(listingTimestamp + attributes.cliffDuration + (attributes.vestingPeriodDuration * 2));

        uint256 tge = (attributes.allocation * attributes.tgeNumerator) / attributes.tgeDenominator;
        uint256 unlock = (attributes.allocation * attributes.cliffNumerator) / attributes.cliffDenominator;
        uint256 installment = (attributes.allocation - tge - unlock) / attributes.vestingPeriodCount;

        assertEq(
            membership.unlocked(membershipId),
            tge + unlock + (installment * 2),
            "it returns tge amount plus cliff unlock plus amount of 2 vesting periods"
        );
    }

    function test_WhenBlockTimestampAtTheEndOfTheLastVestingPeriod()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(
            listingTimestamp + attributes.cliffDuration
                + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration)
        );

        assertEq(membership.unlocked(membershipId), attributes.allocation, "it returns amount of all vested tokens");
    }

    function test_WhenBlockTimestampAfterTheLastVestingPeriodEnd()
        external
        givenAMembershipExists
        whenTgeTimestampIsNonZero
        whenListingTimestampIsNonZero
        whenBlockTimestampIsAfterListingTimestamp
        givenCliffNumeratorGreaterThanZero
    {
        vm.warp(
            listingTimestamp + attributes.cliffDuration
                + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration) + 1
        );

        assertEq(membership.unlocked(membershipId), attributes.allocation, "it returns amount of all vested tokens");
    }
}
