// SPDX-License-Identifier: MIT
pragma solidity 0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

import { IWETH } from "src/libraries/IWETH.sol";

contract WETHSample is ERC20, IWETH {
    constructor() ERC20("WETHSample", "WETH") {
        _mint(_msgSender(), 100000 * 10 ** decimals());
    }

    function deposit() public payable {
        _update(address(0), _msgSender(), msg.value);
    }
}
