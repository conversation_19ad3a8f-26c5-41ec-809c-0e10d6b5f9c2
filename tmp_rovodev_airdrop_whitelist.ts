#!/usr/bin/env bun
/**
 * Airdrop Whitelist Generator
 * 
 * Generates whitelist for the 15M token airdrop with:
 * - 25% TGE unlock
 * - 48h cliff with 25% unlock
 * - 4 months vesting (remaining 50%)
 */

import { ethers } from 'ethers';

// Type definitions matching the Solidity structs
interface MembershipAttributes {
  price: string;              // 0 for airdrop (free)
  allocation: string;         // 15M tokens per recipient
  claimbackPeriod: number;    // 0 for airdrop (no claimback)
  tgeNumerator: number;       // 25% at TGE
  tgeDenominator: number;     
  cliffDuration: number;      // 48 hours
  cliffNumerator: number;     // 25% after cliff
  cliffDenominator: number;   
  vestingPeriodCount: number; // 4 months
  vestingPeriodDuration: number; // 30 days each
  tradeable: number;          // 0 = false (non-tradeable)
}

interface WhitelistEntry {
  address: string;
  attributes: MembershipAttributes;
}

/**
 * Simple Merkle Tree implementation
 */
class AirdropMerkleTree {
  private leaves: string[];
  private layers: string[][];

  constructor(entries: WhitelistEntry[]) {
    this.leaves = entries.map(entry => this.generateLeaf(entry));
    this.buildTree();
  }

  private generateLeaf(entry: WhitelistEntry): string {
    // This matches the VestPresale._requireCallerIsWhitelisted logic
    const encoded = ethers.utils.defaultAbiCoder.encode(
      ['address', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint8'],
      [
        entry.address,
        entry.attributes.price,
        entry.attributes.allocation,
        entry.attributes.claimbackPeriod,
        entry.attributes.tgeNumerator,
        entry.attributes.tgeDenominator,
        entry.attributes.cliffDuration,
        entry.attributes.cliffNumerator,
        entry.attributes.cliffDenominator,
        entry.attributes.vestingPeriodCount,
        entry.attributes.vestingPeriodDuration,
        entry.attributes.tradeable
      ]
    );
    return ethers.utils.keccak256(encoded);
  }

  private buildTree(): void {
    if (this.leaves.length === 0) {
      throw new Error('Cannot build tree with no leaves');
    }

    this.layers = [this.leaves];
    
    while (this.layers[this.layers.length - 1].length > 1) {
      const currentLayer = this.layers[this.layers.length - 1];
      const nextLayer: string[] = [];
      
      for (let i = 0; i < currentLayer.length; i += 2) {
        const left = currentLayer[i];
        const right = i + 1 < currentLayer.length ? currentLayer[i + 1] : ethers.constants.HashZero;
        
        const sortedPair = left < right ? [left, right] : [right, left];
        const hash = ethers.utils.keccak256(
          ethers.utils.concat([sortedPair[0], sortedPair[1]])
        );
        nextLayer.push(hash);
      }
      
      this.layers.push(nextLayer);
    }
  }

  getRoot(): string {
    if (this.layers.length === 0) return ethers.constants.HashZero;
    return this.layers[this.layers.length - 1][0];
  }

  getProof(entry: WhitelistEntry): { leaf: string, proof: string[] } {
    const leaf = this.generateLeaf(entry);
    const leafIndex = this.leaves.indexOf(leaf);
    
    if (leafIndex === -1) {
      throw new Error('Entry not found in tree');
    }

    const proof: string[] = [];
    let currentIndex = leafIndex;

    for (let i = 0; i < this.layers.length - 1; i++) {
      const currentLayer = this.layers[i];
      const siblingIndex = currentIndex % 2 === 0 ? currentIndex + 1 : currentIndex - 1;
      
      if (siblingIndex < currentLayer.length) {
        proof.push(currentLayer[siblingIndex]);
      } else {
        proof.push(ethers.constants.HashZero);
      }
      
      currentIndex = Math.floor(currentIndex / 2);
    }

    return { leaf, proof };
  }
}

// Airdrop configuration (matches your deployment script)
const AIRDROP_ATTRIBUTES: MembershipAttributes = {
  price: "0", // Free airdrop
  allocation: ethers.utils.parseEther("15000000").toString(), // 15M tokens
  claimbackPeriod: 0, // No claimback for airdrops
  tgeNumerator: 1, // 25% unlock at TGE
  tgeDenominator: 4,
  cliffDuration: 48 * 60 * 60, // 48 hours
  cliffNumerator: 1, // 25% unlock after cliff
  cliffDenominator: 4,
  vestingPeriodCount: 4, // 4 months vesting
  vestingPeriodDuration: 30 * 24 * 60 * 60, // 30 days
  tradeable: 0 // Non-tradeable
};

function generateAirdropWhitelist(addresses: string[]): {
  root: string;
  proofs: Record<string, string[]>;
  entries: WhitelistEntry[];
} {
  console.log("🎁 Generating Airdrop Whitelist...");
  
  // Create whitelist entries
  const entries: WhitelistEntry[] = addresses.map(address => ({
    address: address.toLowerCase(),
    attributes: AIRDROP_ATTRIBUTES
  }));

  // Build merkle tree
  const tree = new AirdropMerkleTree(entries);
  const root = tree.getRoot();

  // Generate proofs for all addresses
  const proofs: Record<string, string[]> = {};
  entries.forEach(entry => {
    const { proof } = tree.getProof(entry);
    proofs[entry.address] = proof;
  });

  console.log(`✅ Generated whitelist for ${addresses.length} addresses`);
  console.log(`🌳 Merkle Root: ${root}`);
  
  return { root, proofs, entries };
}

// Example usage
function main() {
  // Example airdrop recipients (replace with your actual list)
  const airdropAddresses = [
    "0x1234567890123456789012345678901234567890",
    "0x2345678901234567890123456789012345678901", 
    "0x3456789012345678901234567890123456789012",
    "0x4567890123456789012345678901234567890123",
    "0x5678901234567890123456789012345678901234"
  ];

  const whitelist = generateAirdropWhitelist(airdropAddresses);

  console.log("\n📋 Airdrop Configuration:");
  console.log(`  Allocation per user: 15,000,000 tokens`);
  console.log(`  TGE unlock: 25% (3,750,000 tokens)`);
  console.log(`  Cliff: 48 hours`);
  console.log(`  Cliff unlock: 25% (3,750,000 tokens)`);
  console.log(`  Vesting: 50% over 4 months (1,875,000 tokens/month)`);

  console.log("\n🔐 Sample Proofs:");
  Object.entries(whitelist.proofs).slice(0, 2).forEach(([address, proof]) => {
    console.log(`\n${address}:`);
    console.log(`  Proof: [${proof.map(p => `"${p}"`).join(', ')}]`);
  });

  console.log("\n🚀 Next Steps:");
  console.log(`1. Update your deployment script with this root:`);
  console.log(`   whitelistRoot: bytes32(${whitelist.root})`);
  console.log(`2. Or update existing round:`);
  console.log(`   presale.updateRoundWhitelist(roundId, "${whitelist.root}", "");`);
  
  return whitelist;
}

// Export for use in other scripts
export { generateAirdropWhitelist, AIRDROP_ATTRIBUTES, AirdropMerkleTree };

// Run if called directly
if (import.meta.main) {
  main();
}