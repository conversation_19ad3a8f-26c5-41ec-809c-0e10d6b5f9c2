// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

contract PresaleUpdateTgeTimestamp is PresaleScript {
    function run(string memory rpcUrl, uint256 timestamp) external {
        initializePresaleScriptConfig(rpcUrl);

        vm.startBroadcast();

        presale.updateTgeTimestamp(timestamp);

        vm.stopBroadcast();
    }
}
