// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Round } from "src/types/Round.sol";

contract PresaleAddRound is PresaleScript {
    function run(
        string memory rpcUrl,
        string memory name,
        uint256 startTimestamp,
        uint256 endTimestamp,
        bytes32 whitelistRoot
    ) external {
        initializePresaleScriptConfig(rpcUrl);

        vm.startBroadcast();

        presale.addRound(
            Round({
                name: name,
                startTimestamp: startTimestamp,
                endTimestamp: endTimestamp,
                whitelistRoot: whitelistRoot,
                proofsUri: "",
                bonusNumerator: 0,
                bonusDenominator: 1,
                // attributes: abi.encode(
                //     IVestMembership.Attributes({
                //         price: 1 * 10 ** 18,
                //         allocation: 100 * 10 ** 18,
                //         claimbackPeriod: 24 hours,
                //         tgeNumerator: 1,
                //         tgeDenominator: 1,
                //         cliffDuration: 0,
                //         cliffNumerator: 0,
                //         cliffDenominator: 1,
                //         vestingPeriodCount: 1,
                //         vestingPeriodDuration: 1,
                //         tradeable: Boolean.FALSE
                //     })
                // )
                attributes: ""
            })
        );

        vm.stopBroadcast();
    }
}
