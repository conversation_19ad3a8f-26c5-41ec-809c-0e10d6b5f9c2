// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import "forge-std/Script.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

/**
 * @title ERC20Transfer
 * @notice Script to transfer ERC20 tokens from the current account to another wallet
 * @dev Usage: forge script script/ERC20Transfer.s.sol:ERC20Transfer --rpc-url <RPC_URL> --broadcast --private-key <PRIVATE_KEY>
 *      Configure the transfer parameters directly in the contract below
 */
contract ERC20Transfer is Script {
    using SafeERC20 for IERC20;

    // ===== CONFIGURE YOUR TRANSFER PARAMETERS HERE =====
    
    // Token contract address (example: USDC on Base)
    address public constant TOKEN_ADDRESS = ******************************************;
    
    // From address (will be the msg.sender/broadcaster)
    // Note: The 'from' address is automatically the account that broadcasts the transaction
    
    // To address (recipient of the tokens)
    address public constant TO_ADDRESS = ******************************************;
    
    // Amount to transfer (in token's smallest unit)
    // Example: For USDC (6 decimals), 1000000 = 1 USDC
    // Example: For most tokens (18 decimals), 1000000000000000000 = 1 token
    uint256 public constant TRANSFER_AMOUNT = *********; // 1 USDC (6 decimals)
    
    // ===== END CONFIGURATION =====

    /**
     * @notice Transfer ERC20 tokens using the configured parameters in the contract
     */
    function run() external {
        _executeTransfer(TOKEN_ADDRESS, TO_ADDRESS, TRANSFER_AMOUNT);
    }

    /**
     * @notice Internal function to execute the transfer
     * @param _tokenAddress The ERC20 token contract address
     * @param _recipientAddress The address to send tokens to
     * @param _amount The amount to transfer (in token's smallest unit)
     */
    function _executeTransfer(address _tokenAddress, address _recipientAddress, uint256 _amount) internal {
        require(_tokenAddress != address(0), "Token address cannot be zero");
        require(_recipientAddress != address(0), "Recipient address cannot be zero");
        require(_amount > 0, "Transfer amount must be greater than zero");

        IERC20 token = IERC20(_tokenAddress);
        
        console.log("=== ERC20 Transfer Script ===");
        console.log("Token Address:", _tokenAddress);
        console.log("From Address (Sender):", msg.sender);
        console.log("To Address (Recipient):", _recipientAddress);
        console.log("Transfer Amount:", _amount);

        // Check sender's balance before transfer
        uint256 senderBalance = token.balanceOf(msg.sender);
        console.log("Sender Balance Before:", senderBalance);
        
        require(senderBalance >= _amount, "Insufficient token balance");

        // Check recipient's balance before transfer
        uint256 recipientBalanceBefore = token.balanceOf(_recipientAddress);
        console.log("Recipient Balance Before:", recipientBalanceBefore);

        vm.startBroadcast();

        // Execute the transfer
        token.safeTransfer(_recipientAddress, _amount);

        vm.stopBroadcast();

        // Check balances after transfer
        uint256 senderBalanceAfter = token.balanceOf(msg.sender);
        uint256 recipientBalanceAfter = token.balanceOf(_recipientAddress);
        
        console.log("Sender Balance After:", senderBalanceAfter);
        console.log("Recipient Balance After:", recipientBalanceAfter);
        console.log("Transfer completed successfully!");
        
        // Verify the transfer
        require(senderBalanceAfter == senderBalance - _amount, "Sender balance mismatch");
        require(recipientBalanceAfter >= recipientBalanceBefore + _amount, "Recipient balance mismatch");
    }
}