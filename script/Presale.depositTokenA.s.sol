// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract PresaleDepositTokenA is PresaleScript {
    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        IERC20 tokenA = presale.tokenA();

        uint256 amount = 2285714 * 10 ** 18;
        uint256 roundId = 1; // FCFS round

        vm.startBroadcast();

        tokenA.approve(address(presale), amount);
        presale.depositTokenA(roundId, amount);

        vm.stopBroadcast();
    }
}
