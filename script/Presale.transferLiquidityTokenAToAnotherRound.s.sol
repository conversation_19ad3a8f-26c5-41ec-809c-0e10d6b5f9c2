// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

contract PresaleTransferLiquidityTokenAToAnotherRound is PresaleScript {
    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        uint256 from = 3;
        uint256 to = 4;
        uint256 amount = 15868529838160000000000000;

        vm.startBroadcast();

        presale.transferLiquidityTokenAToAnotherRound(from, to, amount);

        vm.stopBroadcast();
    }
}
