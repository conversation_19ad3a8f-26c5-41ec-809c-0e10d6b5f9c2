// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import "forge-std/Script.sol";

import { VestPresale } from "src/VestPresale.sol";

contract PresaleScript is Script {
    VestPresale presale;

    function initializePresaleScriptConfig(string memory rpcUrl) internal {
        string memory configurationJson = vm.readFile(string.concat(vm.projectRoot(), "/script/configuration.json"));
        address vestPresaleAddress =
            vm.parseJsonAddress(configurationJson, string.concat(".vestPresaleAddress.", rpcUrl));

        presale = VestPresale(payable(vestPresaleAddress));
    }
}
