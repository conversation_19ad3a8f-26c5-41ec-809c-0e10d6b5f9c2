// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

contract PresaleWithdrawTokenA is PresaleScript {
    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        vm.startBroadcast();

        uint256 roundId = 1;
        uint256 amount = 10073186517700000000000000;

        presale.withdrawTokenA(roundId, amount);

        vm.stopBroadcast();
    }
}
