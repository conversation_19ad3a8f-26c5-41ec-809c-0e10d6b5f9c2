// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { Strings } from "@openzeppelin/contracts/utils/Strings.sol";

import "forge-std/Script.sol";

import { Round } from "src/types/Round.sol";
import { Fees } from "src/types/Fees.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";

import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";
import "forge-std/console.sol";

import { ERC20Sample } from "test/samples/ERC20Sample.sol";

import { VestBuilder } from "script/Vest.builder.s.sol";

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

/**
 * @notice Get data from https://www.notion.so/tenset/d9b8a68b083345d7a1edba0a2f54a4d5
 * @dev When `IS_DEVELOPMENT` is true:
 * - the script will deploy ERC20Sample tokens instead of using the actual tokens.
 * - the script will use default anvil addresses as a manager and beneficiary.
 */
contract PresaleDeploy is VestBuilder {
    using Strings for address;

    // issued by the Beneficiary
    IERC20 internal TOKEN_A = IERC20(0x481FE356DF88169f5F38203Dd7f3C67B7559FDa5);
    uint8 internal DECIMALS_A = 18;

    // token pair
    IERC20 internal TOKEN_B = IERC20(0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913);
    uint8 internal DECIMALS_B = 6;

    address internal manager = 0xd5680C4CCa137c275e5f49A89AB469362d32979C;
    address internal beneficiary = 0x829F63253322A8426e3f13aed8b43F272ACfbE42;

    string internal description = "Rainmaker Presale";

    // TODO
    Fees internal fees = Fees({
        tokenANumerator: 0,
        tokenADenominator: 1,
        tokenBNumerator: 1,
        tokenBDenominator: 10,
        nftNumerator: 0,
        nftDenominator: 1
    });

    Round internal tglpRound = Round({
        name: "TGLP",
        startTimestamp: 1752998400, // UTC Sunday, 20 July 2025 08:00:00
        endTimestamp: 1753171200, // UTC Tuesday, 22 July 2025 08:00:00 (48 hours later)
        whitelistRoot: bytes32(0x8540c1d4b3c3cdaa6fbef0b2f1aa638c6442623f5f9d5206e47eb5ad1c5686ac),
        proofsUri: "",
        bonusNumerator: 0, // No bonus
        bonusDenominator: 1,
        attributes: abi.encode(
            IVestMembership.Attributes({
                price: 350000, // 0.35 USDC in Token B decimals (0.35 * 10^6)
                allocation: 200000 * 10 ** DECIMALS_A, // 200,000 tokens
                claimbackPeriod: 24 hours, // 24 hours claimback period
                tgeNumerator: 25, // 25% unlock at TGE
                tgeDenominator: 100,
                cliffDuration: 0, // No cliff
                cliffNumerator: 0,
                cliffDenominator: 1,
                vestingPeriodCount: 240, // 8 months vesting
                vestingPeriodDuration: 86_400,
                tradeable: Boolean.FALSE
            })
        )
    });

    Round internal fcfsRound = Round({
        name: "FCFS",
        startTimestamp: 1753178400, // UTC Tuesday, 22 July 2025 10:00:00 (2 hours after TGLP ends)
        endTimestamp: 1753264800, // UTC Wednesday, 23 July 2025 10:00:00 (24 hours later)
        whitelistRoot: bytes32(0), // No whitelist for FCFS
        proofsUri: "",
        bonusNumerator: 0, // No bonus
        bonusDenominator: 1,
        attributes: abi.encode(
            IVestMembership.Attributes({
                price: 350000, // 0.35 USDC in Token B decimals (0.35 * 10^6)
                allocation: 200000 * 10 ** DECIMALS_A, // 200,000 tokens
                claimbackPeriod: 24 hours, // 24 hours claimback period
                tgeNumerator: 25, // 25% unlock at TGE
                tgeDenominator: 100,
                cliffDuration: 0, // No cliff
                cliffNumerator: 0,
                cliffDenominator: 1,
                vestingPeriodCount: 240, // 8 months vesting
                vestingPeriodDuration: 86_400,
                tradeable: Boolean.FALSE
            })
        )
    });

    // TGE starts right after rounds end
    uint256 tgeTimestamp = 1753279200; // UTC Wednesday, 23 July 2025 14:00:00 (2:00 PM)
    uint256 listingTimestamp = 1753279200; // Same as TGE - immediate listing

    uint256 MAX_CLAIMBACK_PERIOD = 24 hours;

    function run(string memory rpcUrl) external {
        vm.startBroadcast();

        init(fees);

        Round[] memory rounds = new Round[](2);
        rounds[0] = tglpRound;
        rounds[1] = fcfsRound;

        (address presale,) = deploy(
            Presale.Configuration({
                tokenA: TOKEN_A,
                tokenB: TOKEN_B,
                manager: manager,
                beneficiary: beneficiary,
                tgeTimestamp: tgeTimestamp,
                listingTimestamp: listingTimestamp,
                claimbackPeriod: MAX_CLAIMBACK_PERIOD,
                fees: Presale.Fees({
                    tokenANumerator: fees.tokenANumerator,
                    tokenADenominator: fees.tokenADenominator,
                    tokenBNumerator: fees.tokenBNumerator,
                    tokenBDenominator: fees.tokenBDenominator
                })
            }),
            rounds,
            Membership.Configuration({
                fees: Membership.Fees({ numerator: fees.nftNumerator, denominator: fees.nftDenominator }),
                descriptor: new VestMembershipDescriptor(),
                metadata: IVestMembership.Metadata({ token: address(TOKEN_A), description: description, color: "#ffffff" })
            })
        );

        console.log("Deployer:", msg.sender);

        vm.stopBroadcast();

        console.log("Manager:", manager);
        console.log("Beneficiary:", beneficiary);

        console.log("TokenA:", address(TOKEN_A));
        console.log("TokenB:", address(TOKEN_B));

        console.log("Presale:", presale);

        vm.writeJson(
            Strings.toHexString(uint160(presale), 20),
            "script/configuration.json",
            string.concat(".vestPresaleAddress.", rpcUrl)
        );
    }
}
