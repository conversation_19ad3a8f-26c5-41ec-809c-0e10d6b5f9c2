// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Round } from "src/types/Round.sol";
import { MerkleProof } from "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";
import "forge-std/Script.sol";

contract PresaleBuy is PresaleScript {
    // Hardcoded values for TGLP round purchase
    uint256 internal constant ROUND_ID = 1; // TGLP round ID
    uint256 internal constant AMOUNT_A = 2857142857142857142; // ~2.857 tokens to buy

    // TGLP round attributes (from provided data)
    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: 350000, // 0.35 USDC (6 decimals)
        allocation: 1027362870000000000000, // ~1,027.36 tokens (18 decimals)
        claimbackPeriod: 86400, // 24 hours claimback period
        tgeNumerator: 25, // 25% unlock at TGE
        tgeDenominator: 100,
        cliffDuration: 0, // No cliff
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 240, // 8 months vesting (240 daily periods)
        vestingPeriodDuration: 86400, // Daily vesting (86400 seconds)
        tradeable: Boolean.FALSE
    });

    // Merkle proof for whitelist (real proof data)
    // NOTE: This proof was generated for a specific address and attributes combination.
    // If verification fails, you need to generate a new proof for your address.
    bytes32[] internal proof = [
        bytes32(0x9f9193d27260523ea2b4a77323327301e1a9147561b6336fb702ac1d1f6e08e7),
        bytes32(0xe0f69b4c934429f90ce39160c736189ab3ce913584f7d202b768b33bd6a774ee),
        bytes32(0xbae4c924e640c4a0fa5a23d5389d6134563cfce3f4de530e2cce41c0337653fa),
        bytes32(0x20065ff6859f3d7b50ba22834299039b5492f45ee78f9e1ae6e6be997c5b49bf),
        bytes32(0x274add1f46825b6a88912f12f99fdf2e5d0706a323c2c5320b8d549fd4565f83),
        bytes32(0x945800b541ee4c732048c8d81d7acd87da4f28cc46caae03948f612b2a626d8e),
        bytes32(0xe86723670be8486d80805a386bb449d63e5af1f83340abe6f13152ec513642d8),
        bytes32(0xb6cd27742f834e71d1d66a8a87a6e1e9927b7d8f03a52778959e3ccffe548779),
        bytes32(0xc9fca8c981798f8d814f1004cb5d6076fb88d350bedccabca27be6b7316c7098),
        bytes32(0x98454a3c59ea6a0bee8de0357fb29a7e6cfc8bc0243917ccfe8c38b4432f0375),
        bytes32(0xbc18511363fa831492cf033da96b02c20e63080c05bb428f5784d19e3d77b8ba),
        bytes32(0x937e6e1214071e2665a6af6ed0da07f68fb07b1ce13bbdff16448ac8baf01336)
    ];

    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        // Verify proof before attempting purchase
        _verifyProof(msg.sender);

        vm.startBroadcast();

        // Buy tokens in TGLP round with whitelist proof
        uint256 membershipId = presale.buy(ROUND_ID, AMOUNT_A, attributes, proof);

        vm.stopBroadcast();

        console.log("Purchase completed!");
        console.log("Round ID:", ROUND_ID);
        console.log("Amount A purchased:", AMOUNT_A);
        console.log("Membership ID:", membershipId);
        console.log("Buyer:", msg.sender);
    }


    // Internal function to verify merkle proof before purchase
    function _verifyProof(address account) internal view {
        // Generate the leaf (matching VestPresale contract encoding)
        // Contract uses original attribute types, not uint256
        bytes32 leaf = keccak256(
            abi.encode(
                account, // address
                attributes.price, // uint256
                attributes.allocation, // uint256
                attributes.claimbackPeriod, // uint256
                attributes.tgeNumerator, // uint32
                attributes.tgeDenominator, // uint32
                attributes.cliffDuration, // uint32
                attributes.cliffNumerator, // uint32
                attributes.cliffDenominator, // uint32
                attributes.vestingPeriodCount, // uint32
                attributes.vestingPeriodDuration, // uint32
                attributes.tradeable // uint8
            )
        );

        // Query the actual whitelist root from the contract
        bytes32 whitelistRoot = _getWhitelistRoot(ROUND_ID);

        // Verify the proof
        bool isValid = MerkleProof.verify(proof, whitelistRoot, leaf);

        console.log("=== Merkle Proof Verification ===");
        console.log("Account:", account);
        console.log("Leaf hash:");
        console.logBytes32(leaf);
        console.log("Whitelist root:");
        console.logBytes32(whitelistRoot);
        console.log("Proof valid:", isValid);

        if (!isValid) {
            console.log("PROOF INVALID - This address/attributes combination is not whitelisted");
            console.log("The proof array in this script was generated for a different address.");
            console.log("To fix this:");
            console.log("1. Generate a new proof for address:", account);
            console.log("2. Or use the address this proof was generated for");
        } else {
            console.log("PROOF VALID - This address can participate in TGLP round");
            console.log("Proceeding with purchase...");
        }

        console.log("=====================================");
    }

    // Helper function to get the current whitelist root from the contract
    function _getWhitelistRoot(uint256 roundId) internal view returns (bytes32) {
        try presale.getRound(roundId) returns (Round memory round) {
            console.log("Successfully queried contract for whitelist root");
            return round.whitelistRoot;
        } catch {
            console.log("Failed to query contract - using fallback root");
            // Fallback to known root if contract query fails
            return 0x6c4488cba57379abc15d1c02019328c009178e457a5bfdab89646c08216a9821;
        }
    }
}
