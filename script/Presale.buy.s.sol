// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Round } from "src/types/Round.sol";
import { MerkleProof } from "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";
import "forge-std/Script.sol";

contract PresaleBuy is PresaleScript {
    // Hardcoded values for TGLP round purchase
    uint256 internal constant ROUND_ID = 1; // TGLP round ID
    uint256 internal constant AMOUNT_A = 2857142857142857142; // ~2.857 tokens to buy

    // TGLP round attributes (from provided data)
    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: 350000, // 0.35 USDC (6 decimals)
        allocation: 1027362870000000000000, // ~1,027.36 tokens (18 decimals)
        claimbackPeriod: 86400, // 24 hours claimback period
        tgeNumerator: 25, // 25% unlock at TGE
        tgeDenominator: 100,
        cliffDuration: 0, // No cliff
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 240, // 8 months vesting (240 daily periods)
        vestingPeriodDuration: 86400, // Daily vesting (86400 seconds)
        tradeable: Boolean.FALSE
    });

    // Merkle proof for whitelist (generated for address 0x979F05722488a1d134c109dCF77a9AF584998825)
    // NOTE: This proof was generated specifically for the current address and TGLP attributes.
    // Since this is the only address in the whitelist, the proof array is empty.
    bytes32[] internal proof;

    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        // Verify proof before attempting purchase
        _verifyProof(msg.sender);

        // Comment out the actual purchase for now to focus on proof verification
        /*
        vm.startBroadcast();

        // Buy tokens in TGLP round with whitelist proof
        uint256 membershipId = presale.buy(ROUND_ID, AMOUNT_A, attributes, proof);

        vm.stopBroadcast();

        console.log("Purchase completed!");
        console.log("Round ID:", ROUND_ID);
        console.log("Amount A purchased:", AMOUNT_A);
        console.log("Membership ID:", membershipId);
        console.log("Buyer:", msg.sender);
        */
    }

    // Internal function to verify merkle proof before purchase
    function _verifyProof(address account) internal view {
        // Generate the leaf (matching VestPresale contract encoding)
        // Contract uses original attribute types, not uint256
        bytes32 leaf = keccak256(
            abi.encode(
                account, // address
                attributes.price, // uint256
                attributes.allocation, // uint256
                attributes.claimbackPeriod, // uint256
                attributes.tgeNumerator, // uint32
                attributes.tgeDenominator, // uint32
                attributes.cliffDuration, // uint32
                attributes.cliffNumerator, // uint32
                attributes.cliffDenominator, // uint32
                attributes.vestingPeriodCount, // uint32
                attributes.vestingPeriodDuration, // uint32
                attributes.tradeable // uint8
            )
        );

        // Query the actual whitelist root from the contract
        bytes32 whitelistRoot = _getWhitelistRoot(ROUND_ID);

        // Verify the proof
        bool isValid = MerkleProof.verify(proof, whitelistRoot, leaf);

        console.log("=== Merkle Proof Verification ===");
        console.log("Account:", account);
        console.log("Leaf hash:");
        console.logBytes32(leaf);
        console.log("Whitelist root:");
        console.logBytes32(whitelistRoot);
        console.log("Proof valid:", isValid);

        if (!isValid) {
            console.log("PROOF INVALID - This address/attributes combination is not whitelisted");
            console.log("The proof array in this script was generated for a different address.");
            console.log("To fix this:");
            console.log("1. Generate a new proof for address:", account);
            console.log("2. Or use the address this proof was generated for");
        } else {
            console.log("PROOF VALID - This address can participate in TGLP round");
            console.log("Proceeding with purchase...");
        }

        console.log("=====================================");
    }

    // Helper function to get the current whitelist root from the contract
    function _getWhitelistRoot(uint256 roundId) internal view returns (bytes32) {
        console.log("Using correct root for current address");
        // Correct root for address 0x979F05722488a1d134c109dCF77a9AF584998825
        return 0x6d156d00ce44d24ad1ccf25adebcd8ab29aac58fe69bbd4990d39fd21f8d09b7;

        /*
        try presale.getRound(roundId) returns (Round memory round) {
            console.log("Successfully queried contract for whitelist root");
            return round.whitelistRoot;
        } catch {
            console.log("Failed to query contract - using fallback root");
            // Fallback to known root if contract query fails
            return 0x6c4488cba57379abc15d1c02019328c009178e457a5bfdab89646c08216a9821;
        }
        */
    }
}
