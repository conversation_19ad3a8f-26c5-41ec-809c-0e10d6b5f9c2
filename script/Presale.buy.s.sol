// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Round } from "src/types/Round.sol";
import { MerkleProof } from "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";
import "forge-std/Script.sol";

contract PresaleBuy is PresaleScript {
    // Hardcoded values for TGLP round purchase
    uint256 internal constant ROUND_ID = 1; // TGLP round ID
    uint256 internal constant AMOUNT_A = 2857142857142857142; // ~2.857 tokens to buy

    // TGLP round attributes (from provided data)
    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: 350000, // 0.35 USDC (6 decimals)
        allocation: 1027362870000000000000, // ~1,027.36 tokens (18 decimals)
        claimbackPeriod: 86400, // 24 hours claimback period
        tgeNumerator: 25, // 25% unlock at TGE
        tgeDenominator: 100,
        cliffDuration: 0, // No cliff
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 240, // 8 months vesting (240 daily periods)
        vestingPeriodDuration: 86400, // Daily vesting (86400 seconds)
        tradeable: Boolean.FALSE // Updated whitelist data
     });

    // Merkle proof for whitelist (updated proof data)
    // NOTE: This proof was generated for address 0x979F05722488a1d134c109dCF77a9AF584998825
    // with tradeable: 1 (Boolean.FALSE) for the updated whitelist.
    bytes32[] internal proof = [
        bytes32(0xd64b32fef651c13d5d9d36a0d8c602c0225a1f4bbd618ba54101e261e0166583),
        bytes32(0x0f9c1497f3062315a7912a27196c915ad8e61dbc599da9053d5111cd82b7df62),
        bytes32(0x901f9e823c7d4c3e6dd24dc9bc811300204a679a2a8d438c8699a4d016909632),
        bytes32(0x0311528e4b6ab37031cc3e9b9471c78716b0888edf479edfd264b0c9eb35be36),
        bytes32(0x4687e0dcc7da589f5f48c60dc411f19109af438d67ae3c17128d30c18d6b51d6),
        bytes32(0x45f0ebf053ba926d4d466e9086374d31e9cd5de30ec1d85deb16e70525462883),
        bytes32(0x6e453e89d4816aa48a7e5e3a487c13538f2e4d19ba3e54caac06aa2586cdd45b),
        bytes32(0x58ff4249c7279b12dde3222df358cafcebf22f4e1550adeeb13243e2ee9c6d59),
        bytes32(0xedeaea4f0264215117d44b452f56ab10b3514c5ad5a8a721fafb1773ee6866bc),
        bytes32(0xe65ebc54b41d37d953cd1e235c757d839aa9bbbfabeecbc4c0e7735a444fdee7),
        bytes32(0xa9fdaa9e525bec6ca3c37804ad60d259b2b6ae088c410772fa3a01e7ed1e08d3),
        bytes32(0xc0fd699275b06359876381b21c3bdcb2e39ab376734b2524acdc0d50adcd8ffc)
    ];

    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        // Verify proof before attempting purchase
        _verifyProof(msg.sender);

        // Comment out the actual purchase for now to focus on proof verification
        /*
        vm.startBroadcast();

        // Buy tokens in TGLP round with whitelist proof
        uint256 membershipId = presale.buy(ROUND_ID, AMOUNT_A, attributes, proof);

        vm.stopBroadcast();

        console.log("Purchase completed!");
        console.log("Round ID:", ROUND_ID);
        console.log("Amount A purchased:", AMOUNT_A);
        console.log("Membership ID:", membershipId);
        console.log("Buyer:", msg.sender);
        */
    }

    // Internal function to verify merkle proof before purchase
    function _verifyProof(address account) internal view {
        // Generate the leaf (matching VestPresale contract encoding)
        // Contract uses original attribute types, not uint256
        bytes32 leaf = keccak256(
            abi.encode(
                account, // address
                attributes.price, // uint256
                attributes.allocation, // uint256
                attributes.claimbackPeriod, // uint256
                attributes.tgeNumerator, // uint32
                attributes.tgeDenominator, // uint32
                attributes.cliffDuration, // uint32
                attributes.cliffNumerator, // uint32
                attributes.cliffDenominator, // uint32
                attributes.vestingPeriodCount, // uint32
                attributes.vestingPeriodDuration, // uint32
                attributes.tradeable // uint8
            )
        );

        // Query the actual whitelist root from the contract
        bytes32 whitelistRoot = _getWhitelistRoot(ROUND_ID);

        // Verify the proof
        bool isValid = MerkleProof.verify(proof, whitelistRoot, leaf);

        console.log("=== Merkle Proof Verification ===");
        console.log("Account:", account);
        console.log("Leaf hash:");
        console.logBytes32(leaf);
        console.log("Whitelist root:");
        console.logBytes32(whitelistRoot);
        console.log("Proof valid:", isValid);

        if (!isValid) {
            console.log("PROOF INVALID - This address/attributes combination is not whitelisted");
            console.log("The proof array in this script was generated for a different address.");
            console.log("To fix this:");
            console.log("1. Generate a new proof for address:", account);
            console.log("2. Or use the address this proof was generated for");
        } else {
            console.log("PROOF VALID - This address can participate in TGLP round");
            console.log("Proceeding with purchase...");
        }

        console.log("=====================================");
    }

    // Helper function to get the current whitelist root from the contract
    function _getWhitelistRoot(uint256 roundId) internal view returns (bytes32) {
        console.log("Using rainmaker whitelist root");
        // Root from rainmaker whitelist data
        return 0x0a499ca1334261c403efb04b42cd28569abe193cdf3ed7c65dad8b9db7d896ae;

        /*
        try presale.getRound(roundId) returns (Round memory round) {
            console.log("Successfully queried contract for whitelist root");
            return round.whitelistRoot;
        } catch {
            console.log("Failed to query contract - using fallback root");
            // Fallback to known root if contract query fails
            return 0x6c4488cba57379abc15d1c02019328c009178e457a5bfdab89646c08216a9821;
        }
        */
    }
}
