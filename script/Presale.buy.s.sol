// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Round } from "src/types/Round.sol";
import { MerkleProof } from "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";
import "forge-std/Script.sol";

contract PresaleBuy is PresaleScript {
    // Hardcoded values for TGLP round purchase
    uint256 internal constant ROUND_ID = 1; // TGLP round ID
    uint256 internal constant AMOUNT_A = 2857142857142857142; // ~2.857 tokens to buy

    // TGLP round attributes (matching proof data)
    IVestMembership.Attributes internal attributes = IVestMembership.Attributes({
        price: 10000, // 0.01 USDC (6 decimals) - matching proof
        allocation: 1027362870000000000000, // ~1,027.36 tokens (18 decimals)
        claimbackPeriod: 86400, // 24 hours claimback period
        tgeNumerator: 25, // 25% unlock at TGE
        tgeDenominator: 100,
        cliffDuration: 0, // No cliff
        cliffNumerator: 0,
        cliffDenominator: 1,
        vestingPeriodCount: 240, // 8 months vesting (240 daily periods)
        vestingPeriodDuration: 86400, // Daily vesting (86400 seconds)
        tradeable: Boolean.FALSE // Updated whitelist data
     });

    // Merkle proof for whitelist (updated proof data)
    // NOTE: This proof was generated for address 0x6a966461f7bbb7d40d1bb771c0bce49dd66b7d14
    // with price: 10000 and tradeable: 1 (Boolean.FALSE) for the updated whitelist.
    bytes32[] internal proof = [
        bytes32(0x1b0dc954feb2a0e1d8420f35f574d3c403e67923ab51ad03cf10f08c30edb093),
        bytes32(0xe8636708b5486ba74667dd582c533cad878ddccfb9902a24cfdbda7af0db5ebd),
        bytes32(0xf4d5b301897c548a1e4b5a82ac722a2ed2de954c648997355c7c7063b17e6376),
        bytes32(0x08e79983ef631c86af9ac1189b694df029d6affb1969157067ae39e6b52426cd),
        bytes32(0x5b44ac39029fe4e8f438d1f3017c34167966b36f21981ddccc2d59e0e9107cb5),
        bytes32(0xdcddc43c61b622e8753b1aaa89c650a5413d2fdfdb90a5cc43f09e00614fef27),
        bytes32(0x81b0910860e8f8f15cb98b25a375da4cb88faf67e8c02e2a7649b5cefac38e7d),
        bytes32(0x72e4890b07de24acfe1abfb2b56ffaffad378beddac30a344d841eec20db122c),
        bytes32(0xcefeeedd2f82d0067bc767eb38f2ef008d48c6f62374edad5b9feffb3b38936d),
        bytes32(0x0976bcbce84dafb4baf82786aa3ca8937716cdcc4ccc1cde8b86dbe7f8272319),
        bytes32(0x3f74d2e18b48781435b70963bdddab188bab09a6e3d724f6747bb0ab83230949),
        bytes32(0xa941ff928b067933ed24fc117ebd1b8bb252d0af47d955ad5b821a9b4ad06b5d)
    ];

    function run(string memory rpcUrl) external {
        initializePresaleScriptConfig(rpcUrl);

        // Verify proof before attempting purchase
        _verifyProof(msg.sender);

        // Comment out the actual purchase for now to focus on proof verification

        vm.startBroadcast();

        // Buy tokens in TGLP round with whitelist proof
        uint256 membershipId = presale.buy(ROUND_ID, AMOUNT_A, attributes, proof);

        vm.stopBroadcast();

        console.log("Purchase completed!");
        console.log("Round ID:", ROUND_ID);
        console.log("Amount A purchased:", AMOUNT_A);
        console.log("Membership ID:", membershipId);
        console.log("Buyer:", msg.sender);
    }

    // Internal function to verify merkle proof before purchase
    function _verifyProof(address account) internal view {
        // Generate the leaf (matching VestPresale contract encoding)
        // Contract uses original attribute types, not uint256
        bytes32 leaf = keccak256(
            abi.encode(
                account, // address
                attributes.price, // uint256
                attributes.allocation, // uint256
                attributes.claimbackPeriod, // uint256
                attributes.tgeNumerator, // uint32
                attributes.tgeDenominator, // uint32
                attributes.cliffDuration, // uint32
                attributes.cliffNumerator, // uint32
                attributes.cliffDenominator, // uint32
                attributes.vestingPeriodCount, // uint32
                attributes.vestingPeriodDuration, // uint32
                attributes.tradeable // uint8
            )
        );

        // Query the actual whitelist root from the contract
        bytes32 whitelistRoot = _getWhitelistRoot(ROUND_ID);

        // Verify the proof
        bool isValid = MerkleProof.verify(proof, whitelistRoot, leaf);

        console.log("=== Merkle Proof Verification ===");
        console.log("Account:", account);
        console.log("Leaf hash:");
        console.logBytes32(leaf);
        console.log("Whitelist root:");
        console.logBytes32(whitelistRoot);
        console.log("Proof valid:", isValid);

        if (!isValid) {
            console.log("PROOF INVALID - This address/attributes combination is not whitelisted");
            console.log("The proof array in this script was generated for a different address.");
            console.log("To fix this:");
            console.log("1. Generate a new proof for address:", account);
            console.log("2. Or use the address this proof was generated for");
        } else {
            console.log("PROOF VALID - This address can participate in TGLP round");
            console.log("Proceeding with purchase...");
        }

        console.log("=====================================");
    }

    // Helper function to get the current whitelist root from the contract
    function _getWhitelistRoot(uint256 roundId) internal view returns (bytes32) {
        console.log("Using rainmaker whitelist root");
        // Root from rainmaker whitelist data
        return 0x0a499ca1334261c403efb04b42cd28569abe193cdf3ed7c65dad8b9db7d896ae;

        /*
        try presale.getRound(roundId) returns (Round memory round) {
            console.log("Successfully queried contract for whitelist root");
            return round.whitelistRoot;
        } catch {
            console.log("Failed to query contract - using fallback root");
            // Fallback to known root if contract query fails
            return 0x6c4488cba57379abc15d1c02019328c009178e457a5bfdab89646c08216a9821;
        }
        */
    }
}
