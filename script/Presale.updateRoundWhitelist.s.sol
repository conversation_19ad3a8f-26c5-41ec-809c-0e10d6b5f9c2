// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

contract PresaleUpdateRoundWhitelist is PresaleScript {
    function run(string memory rpcUrl, uint256 roundId, bytes32 whitelistRoot) external {
        initializePresaleScriptConfig(rpcUrl);

        vm.startBroadcast();

        presale.updateRoundWhitelist(roundId, whitelistRoot, "");

        vm.stopBroadcast();
    }
}
