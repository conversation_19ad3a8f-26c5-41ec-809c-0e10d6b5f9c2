// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { Participants } from "../../test/utils/Participants.sol";
import { Uint256Helper } from "../../test/utils/Uint256Helper.sol";

import "forge-std/Script.sol";

import { Round } from "src/types/Round.sol";
import { Fees } from "src/types/Fees.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { VestPresale } from "src/VestPresale.sol";

import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";
import "forge-std/console.sol";

import { VestBuilder } from "script/Vest.builder.s.sol";

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract StaynexProduction is VestBuilder {
    address internal TOKEN_A = 0x62fAC6Fc252E40015B19D188529247E2A24f0A4E;
    address internal TOKEN_B = 0x55d398326f99059fF775485246999027B3197955;

    uint8 internal decimalsA = 18;
    uint8 internal decimalsB = 18;

    function run() external {
        vm.startBroadcast();

        Fees memory fees = Fees({
            tokenANumerator: 0,
            tokenADenominator: 1,
            tokenBNumerator: 1,
            tokenBDenominator: 10,
            nftNumerator: 1,
            nftDenominator: 10
        });

        init(fees);

        Round memory round1 = Round({
            name: "TGLP",
            startTimestamp: 1727780400, // Tuesday, 1 October 2024 11:00:00
            endTimestamp: 1727866800, // Wednesday, 2 October 2024 11:00:00
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 0,
            bonusDenominator: 100,
            attributes: ""
        });

        Round[] memory rounds = new Round[](1);
        rounds[0] = round1;

        VestMembershipDescriptor descriptor = new VestMembershipDescriptor();

        (address presale,) = deploy(
            Presale.Configuration({
                tokenA: IERC20(TOKEN_A),
                tokenB: IERC20(TOKEN_B),
                manager: 0x6ECdb52AE856D13AE6A94aAc1077ccf2bC877578,
                beneficiary: 0x91Fc9f6b58B68d3BdB4917b774A9b29CEA6118aF,
                tgeTimestamp: 0,
                listingTimestamp: 0,
                claimbackPeriod: 3 days,
                fees: Presale.Fees({
                    tokenANumerator: fees.tokenANumerator,
                    tokenADenominator: fees.tokenADenominator,
                    tokenBNumerator: fees.tokenBNumerator,
                    tokenBDenominator: fees.tokenBDenominator
                })
            }),
            rounds,
            Membership.Configuration({
                fees: Membership.Fees({ numerator: fees.nftNumerator, denominator: fees.nftDenominator }),
                descriptor: descriptor,
                metadata: IVestMembership.Metadata({
                    token: address(TOKEN_A),
                    description: "Staynex vesting",
                    color: "#ffffff"
                })
            })
        );

        vm.stopBroadcast();

        console.log("presale", presale);
    }
}
