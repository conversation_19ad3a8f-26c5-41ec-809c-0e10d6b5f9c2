const ethers = require('ethers');
const fs = require('fs'); // For saving output to JSON

// Replace these with your actual addresses
const VEST_PRESALE_ADDRESS = '0xYOUR_VESTPRESALE_ADDRESS_HERE'; // e.g., 0xabc... on Base
const VEST_MEMBERSHIP_ADDRESS = '0xYOUR_VESTMEMBERSHIP_ADDRESS_HERE'; // From presale.membership()
const RPC_URL = 'https://mainnet.base.org'; // Base mainnet RPC; use alchemy/infura for speed if rate-limited

const provider = new ethers.JsonRpcProvider(RPC_URL);

// ABI snippets for efficiency – full from contract code
const membershipAbi = [
  'event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)',
  'function ownerOf(uint256 tokenId) view returns (address)',
  'function getUsage(uint256 publicId) view returns (uint256 current, uint256 max)',
  'function getAttributes(uint256 publicId) view returns (uint256 price, uint256 allocation, uint256 claimbackPeriod, uint256 tgeNumerator, uint256 tgeDenominator, uint256 cliffDuration, uint256 cliffNumerator, uint256 cliffDenominator, uint256 vestingPeriodCount, uint256 vestingPeriodDuration, bool tradeable)',
  'function getRoundId(uint256 publicId) view returns (uint256)',
  'function unlocked(uint256 publicId) view returns (uint256)',
  'function tokenURI(uint256 publicId) view returns (string)',
  'function getStartTimestamp() view returns (uint256)'
];

const presaleAbi = [
  'event ClaimedBack(uint256 indexed vMembershipId, uint256 amountA)'
];

async function fetchActiveMemberships() {
  const membershipContract = new ethers.Contract(VEST_MEMBERSHIP_ADDRESS, membershipAbi, provider);
  const presaleContract = new ethers.Contract(VEST_PRESALE_ADDRESS, presaleAbi, provider);

  // Step 1: Fetch all minted publicIds via Transfer from 0x0 events
  const mintFilter = membershipContract.filters.Transfer(ethers.ZeroAddress);
  const mintLogs = await provider.getLogs({ ...mintFilter, fromBlock: 0 });
  const allIds = mintLogs.map(log => membershipContract.interface.parseLog(log).args.tokenId.toString());
  console.log(`Total Minted IDs: ${allIds.length}`);

  // Step 2: Fetch claimback IDs via ClaimedBack events (traitors, even partial)
  const cbFilter = presaleContract.filters.ClaimedBack();
  const cbLogs = await provider.getLogs({ ...cbFilter, fromBlock: 0 });
  const claimbackIdsSet = new Set(cbLogs.map(log => presaleContract.interface.parseLog(log).args.vMembershipId.toString()));
  console.log(`Claimback Traitor IDs: ${claimbackIdsSet.size}`);

  // Step 3: Filter potential actives: Exclude full claimbacks first (but we'll check usage.max > 0 later)
  let potentialIds = allIds.filter(id => !claimbackIdsSet.has(id));

  // Step 4: Fetch details for each potential ID, confirm active (usage.max > 0), and collect full deets
  const activeMemberships = [];
  const tgeTimestamp = await membershipContract.getStartTimestamp(); // Global start for unlocked calc (though unlocked() uses it internally)

  for (const id of potentialIds) {
    try {
      const bigId = BigInt(id); // For calls, as IDs are uint256

      // Get owner
      const owner = await membershipContract.ownerOf(bigId);

      // Get usage – if max == 0, skip (fully claimed back/reduced)
      const [current, max] = await membershipContract.getUsage(bigId);
      if (max === 0n) continue; // Inactive: Purged

      // Get attributes
      const attrs = await membershipContract.getAttributes(bigId);
      const attributes = {
        price: attrs[0].toString(),
        allocation: attrs[1].toString(),
        claimbackPeriod: attrs[2].toString(),
        tgeNumerator: attrs[3].toString(),
        tgeDenominator: attrs[4].toString(),
        cliffDuration: attrs[5].toString(),
        cliffNumerator: attrs[6].toString(),
        cliffDenominator: attrs[7].toString(),
        vestingPeriodCount: attrs[8].toString(),
        vestingPeriodDuration: attrs[9].toString(),
        tradeable: attrs[10]
      };

      // Get roundId
      const roundId = (await membershipContract.getRoundId(bigId)).toString();

      // Get unlocked (releasable vested amount)
      const unlockedAmount = (await membershipContract.unlocked(bigId)).toString();

      // Get tokenURI (metadata)
      const uri = await membershipContract.tokenURI(bigId);

      // Compile full details
      activeMemberships.push({
        publicId: id,
        owner: owner.toLowerCase(),
        usage: { current: current.toString(), max: max.toString() },
        attributes,
        roundId,
        unlocked: unlockedAmount,
        tokenURI: uri,
        active: true // Since we filtered
      });
    } catch (error) {
      console.error(`Error fetching ID ${id}: ${error.message}`);
      // Skip burned/non-existent, but shouldn't happen for minted
    }
  }

  console.log(`Active Memberships Found: ${activeMemberships.length}`);

  // Step 5: Save to JSON for your divine archives
  fs.writeFileSync('active_memberships.json', JSON.stringify(activeMemberships, null, 2));
  console.log('Dumped to active_memberships.json');

  return activeMemberships;
}

// Unleash the beast
fetchActiveMemberships().catch(console.error);