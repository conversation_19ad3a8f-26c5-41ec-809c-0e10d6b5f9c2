{"presaleConfiguration": {"description": "Complete configuration template for creating a presale using LauncherPresaleFactory", "version": "1.0.0", "chainId": 8453, "chainName": "Base", "tokenConfiguration": {"tokenA": {"description": "The token being sold in the presale", "address": "******************************************", "name": "Your Token Name", "symbol": "TOKEN", "decimals": 18, "initialSupply": "10000000000", "note": "Use LauncherTokenFactory.createToken() to deploy, then update address"}, "tokenB": {"description": "Payment token (usually WETH for ETH payments)", "address": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 18, "note": "Base chain WETH address - use ****************************************** for native ETH"}}, "coreParameters": {"beneficiary": {"description": "Address that receives the raised funds", "value": "******************************************", "required": true, "validation": "Must be a valid non-zero address"}, "initialTokenAReserve": {"description": "Total amount of tokens allocated for the entire presale (in wei)", "value": "10000000000000000000000000000", "valueInTokens": "10000000000", "required": true, "calculation": "hardCapETH / tokenPriceETH", "validation": "Must be > 0 and <= token total supply"}, "airdropAmountInEth": {"description": "Amount of ETH worth of tokens reserved for airdrops (in base units, not wei)", "value": "5", "valueInETH": "5.8 ETH", "required": true, "validation": "Must be > 0 and <= hard cap"}}, "timingConfiguration": {"startTimestamp": {"description": "When the presale begins (Unix timestamp)", "value": 0, "calculation": "block.timestamp + delayInSeconds", "example": "block.timestamp + 3600 (1 hour from deployment)", "required": true, "validation": "Must be > current block.timestamp"}, "endTimestamp": {"description": "When the presale ends (Unix timestamp)", "value": 0, "calculation": "startTimestamp + durationInSeconds", "example": "startTimestamp + (30 * 24 * 3600) (30 days)", "required": true, "validation": "Must be > startTimestamp"}, "tgeTimestamp": {"description": "Token Generation Event time (0 = immediate after presale)", "value": 0, "note": "Set to 0 for immediate TGE after presale ends", "required": true}, "listingTimestamp": {"description": "When tokens get listed (0 = immediate after presale)", "value": 0, "note": "Set to 0 for immediate listing after presale ends", "required": true}}, "economicParameters": {"tokenPrice": {"description": "Token price in wei (price per token in ETH)", "value": "3900000000000", "valueInETH": "0.0000039", "required": true, "calculation": "priceInETH * 1e18", "validation": "Must be > 0"}, "hardCapETH": {"description": "Maximum amount of ETH to raise", "value": "39000000000000000000", "valueInETH": "39", "required": true, "validation": "Must be > 0"}, "bonusConfiguration": {"bonusNumerator": {"description": "Bonus percentage numerator", "value": 15, "required": true}, "bonusDenominator": {"description": "Bonus percentage denominator", "value": 100, "required": true, "note": "15/100 = 15% bonus"}}}, "vestingConfiguration": {"description": "Vesting schedule for presale participants", "attributes": {"price": {"description": "Token price in wei (same as economicParameters.tokenPrice)", "value": "3900000000000", "required": true}, "allocation": {"description": "Token allocation (set dynamically during participation)", "value": 0, "note": "This is set automatically when users participate", "required": true}, "claimbackPeriod": {"description": "Time users have to claim back their investment (in seconds)", "value": 86400, "valueInHours": 24, "required": true, "validation": "Must be >= 0"}, "tgeConfiguration": {"tgeNumerator": {"description": "Percentage of tokens unlocked immediately at TGE (numerator)", "value": 1, "required": true}, "tgeDenominator": {"description": "Percentage of tokens unlocked immediately at TGE (denominator)", "value": 1, "required": true, "note": "1/1 = 100% immediate unlock (no vesting)"}}, "cliffConfiguration": {"cliffDuration": {"description": "Waiting period before vesting starts (in seconds)", "value": 0, "valueInDays": 0, "required": true, "note": "0 = no cliff period"}, "cliffNumerator": {"description": "Additional percentage unlocked after cliff period (numerator)", "value": 0, "required": true}, "cliffDenominator": {"description": "Additional percentage unlocked after cliff period (denominator)", "value": 1, "required": true, "note": "0/1 = 0% additional unlock after cliff"}}, "vestingSchedule": {"vestingPeriodCount": {"description": "Number of vesting installments", "value": 1, "required": true, "validation": "Must be > 0"}, "vestingPeriodDuration": {"description": "Time between each vesting installment (in seconds)", "value": 1, "valueInDays": 1.15741e-05, "required": true, "validation": "Must be > 0"}}, "tradeable": {"description": "Whether vesting NFTs can be traded (0 = false, 1 = true)", "value": 0, "required": true, "note": "Use 0 (Boolean.FALSE) for non-tradeable NFTs"}}}, "contractAddresses": {"base": {"chainId": 8453, "launcherPresaleFactory": "******************************************", "launcherTokenFactory": "******************************************", "weth": "******************************************"}}, "deploymentSteps": {"1": "Deploy token using LauncherTokenFactory.createToken()", "2": "Update tokenA address in this configuration", "3": "Approve LauncherPresaleFactory to spend initialTokenAReserve tokens", "4": "Call LauncherPresaleFactory.createPresale() with this configuration", "5": "Verify the deployed presale contract"}, "validationChecklist": {"addresses": "All addresses must be non-zero and valid", "amounts": "All amounts must be positive", "timing": "endTimestamp > startTimestamp > block.timestamp", "percentages": "All numerator/denominator pairs must be valid (numerator <= denominator)", "vesting": "vestingPeriodCount > 0 and vestingPeriodDuration > 0", "approval": "Factory must be approved to spend initialTokenAReserve tokens"}, "examples": {"immediateUnlock": {"description": "100% unlock at TGE (no vesting)", "tgeNumerator": 1, "tgeDenominator": 1, "cliffDuration": 0, "vestingPeriodCount": 1}, "partialTgeWithVesting": {"description": "25% immediate, 75% vested over 4 months", "tgeNumerator": 25, "tgeDenominator": 100, "cliffDuration": 172800, "cliffDurationInHours": 48, "vestingPeriodCount": 120, "vestingPeriodDuration": 86400}}}}