// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Test } from "forge-std/Test.sol";
import { console } from "forge-std/console.sol";
import { Participants } from "../test/utils/Participants.sol";

/**
 * @title WhitelistExample
 * @notice Example contract demonstrating how to create and manage whitelists
 *         using the existing Participants.sol library for VestPresale integration
 */
contract WhitelistExample is Test {
    using Participants for Participants.Collection;

    Participants.Collection private participants;
    
    // Example addresses for demonstration
    address private constant SEED_INVESTOR_1 = 0x1111111111111111111111111111111111111111;
    address private constant SEED_INVESTOR_2 = 0x2222222222222222222222222222222222222222;
    address private constant PRIVATE_INVESTOR_1 = 0x3333333333333333333333333333333333333333;
    address private constant PRIVATE_INVESTOR_2 = 0x4444444444444444444444444444444444444444;
    address private constant PUBLIC_INVESTOR_1 = 0x5555555555555555555555555555555555555555;
    address private constant TEAM_MEMBER_1 = 0x6666666666666666666666666666666666666666;

    /**
     * @notice Example 1: Create a simple whitelist with uniform terms
     */
    function example_CreateSimpleWhitelist() external {
        console.log("=== Example 1: Simple Whitelist ===");
        
        uint256 roundId = 1;
        
        // Clear any existing participants for this round
        participants.clear(roundId);
        
        // Add participants with identical terms
        address[] memory addresses = new address[](3);
        addresses[0] = PUBLIC_INVESTOR_1;
        addresses[1] = ******************************************;
        addresses[2] = ******************************************;
        
        for (uint256 i = 0; i < addresses.length; i++) {
            participants.add(
                roundId,
                addresses[i],
                0.1 ether,           // price: 0.1 ETH per token
                1000 ether,          // allocation: 1000 tokens max
                24 hours,            // claimbackPeriod: 24 hours
                25,                  // tgeNumerator: 25%
                100,                 // tgeDenominator: 100%
                0,                   // cliffDuration: no cliff
                0,                   // cliffNumerator: 0%
                1,                   // cliffDenominator: 1%
                6,                   // vestingPeriodCount: 6 periods
                30 days,             // vestingPeriodDuration: 30 days each
                1                    // tradeable: true
            );
        }
        
        // Generate the Merkle root and proofs
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        
        console.log("Whitelist Root:", vm.toString(whitelistRoot));
        console.log("Participants:", addresses.length);
        
        // Example: Get proof for first participant
        Participants.Participant memory participant = participants.get(roundId, addresses[0]);
        console.log("Proof length for", vm.toString(addresses[0]), ":", participant.proof.length);
    }

    /**
     * @notice Example 2: Create a multi-tier whitelist with different terms
     */
    function example_CreateMultiTierWhitelist() external {
        console.log("=== Example 2: Multi-Tier Whitelist ===");
        
        uint256 roundId = 2;
        participants.clear(roundId);
        
        // Seed Tier - Best terms
        participants.add(
            roundId,
            SEED_INVESTOR_1,
            0.05 ether,          // price: 0.05 ETH (50% discount)
            10000 ether,         // allocation: 10,000 tokens
            7 days,              // claimbackPeriod: 7 days
            15,                  // tgeNumerator: 15%
            100,                 // tgeDenominator: 100%
            60 days,             // cliffDuration: 60 days
            10,                  // cliffNumerator: 10%
            100,                 // cliffDenominator: 100%
            18,                  // vestingPeriodCount: 18 months
            30 days,             // vestingPeriodDuration: 30 days
            0                    // tradeable: false initially
        );
        
        participants.add(
            roundId,
            SEED_INVESTOR_2,
            0.05 ether,          // Same terms as other seed investor
            15000 ether,         // Higher allocation for larger investor
            7 days,
            15,
            100,
            60 days,
            10,
            100,
            18,
            30 days,
            0
        );
        
        // Private Tier - Standard terms
        participants.add(
            roundId,
            PRIVATE_INVESTOR_1,
            0.08 ether,          // price: 0.08 ETH
            5000 ether,          // allocation: 5,000 tokens
            5 days,              // claimbackPeriod: 5 days
            20,                  // tgeNumerator: 20%
            100,                 // tgeDenominator: 100%
            30 days,             // cliffDuration: 30 days
            15,                  // cliffNumerator: 15%
            100,                 // cliffDenominator: 100%
            12,                  // vestingPeriodCount: 12 months
            30 days,             // vestingPeriodDuration: 30 days
            1                    // tradeable: true
        );
        
        participants.add(
            roundId,
            PRIVATE_INVESTOR_2,
            0.08 ether,          // Same price
            3000 ether,          // Lower allocation
            5 days,
            20,
            100,
            30 days,
            15,
            100,
            12,
            30 days,
            1
        );
        
        // Public Tier - Highest price, shortest vesting
        participants.add(
            roundId,
            PUBLIC_INVESTOR_1,
            0.12 ether,          // price: 0.12 ETH (public price)
            1000 ether,          // allocation: 1,000 tokens
            24 hours,            // claimbackPeriod: 24 hours
            25,                  // tgeNumerator: 25%
            100,                 // tgeDenominator: 100%
            0,                   // cliffDuration: no cliff
            0,                   // cliffNumerator: 0%
            1,                   // cliffDenominator: 1%
            6,                   // vestingPeriodCount: 6 months
            30 days,             // vestingPeriodDuration: 30 days
            1                    // tradeable: true
        );
        
        // Team Allocation - Special terms
        participants.add(
            roundId,
            TEAM_MEMBER_1,
            0,                   // price: 0 (free allocation)
            50000 ether,         // allocation: 50,000 tokens
            0,                   // claimbackPeriod: 0 (no claimback)
            0,                   // tgeNumerator: 0% (no TGE release)
            100,                 // tgeDenominator: 100%
            365 days,            // cliffDuration: 1 year cliff
            0,                   // cliffNumerator: 0%
            1,                   // cliffDenominator: 1%
            36,                  // vestingPeriodCount: 36 months
            30 days,             // vestingPeriodDuration: 30 days
            0                    // tradeable: false
        );
        
        // Generate whitelist root
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        
        console.log("Multi-tier Whitelist Root:", vm.toString(whitelistRoot));
        console.log("Total participants:", 6);
        
        // Show different proof lengths (should all be the same for a balanced tree)
        console.log("Seed investor proof length:", participants.get(roundId, SEED_INVESTOR_1).proof.length);
        console.log("Private investor proof length:", participants.get(roundId, PRIVATE_INVESTOR_1).proof.length);
        console.log("Public investor proof length:", participants.get(roundId, PUBLIC_INVESTOR_1).proof.length);
        console.log("Team member proof length:", participants.get(roundId, TEAM_MEMBER_1).proof.length);
    }

    /**
     * @notice Example 3: Demonstrate proof verification
     */
    function example_VerifyProofs() external {
        console.log("=== Example 3: Proof Verification ===");
        
        uint256 roundId = 3;
        participants.clear(roundId);
        
        // Add a single participant for testing
        participants.add(
            roundId,
            SEED_INVESTOR_1,
            0.1 ether,
            1000 ether,
            24 hours,
            20,
            100,
            0,
            0,
            1,
            12,
            30 days,
            1
        );
        
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        Participants.Participant memory participant = participants.get(roundId, SEED_INVESTOR_1);
        
        // Manually verify the proof using the same logic as VestPresale
        bytes32 leaf = keccak256(
            abi.encode(
                participant.account,
                participant.price,
                participant.allocation,
                participant.claimbackPeriod,
                participant.tgeNumerator,
                participant.tgeDenominator,
                participant.cliffDuration,
                participant.cliffNumerator,
                participant.cliffDenominator,
                participant.vestingPeriodCount,
                participant.vestingPeriodDuration,
                participant.tradeable
            )
        );
        
        console.log("Generated leaf:", vm.toString(leaf));
        console.log("Whitelist root:", vm.toString(whitelistRoot));
        console.log("Proof elements:", participant.proof.length);
        
        // In a real scenario, you would use MerkleProof.verify() here
        // This demonstrates the data that would be passed to the verification function
    }

    /**
     * @notice Run all examples
     */
    function runAllExamples() external {
        example_CreateSimpleWhitelist();
        console.log("");
        example_CreateMultiTierWhitelist();
        console.log("");
        example_VerifyProofs();
    }
}
