/**
 * Frontend Integration Example
 * 
 * This example shows how to integrate whitelist functionality into a frontend application
 * for users to participate in the VestPresale.
 */

import { ethers } from 'ethers';

// Types for frontend integration
interface UserWhitelistData {
  address: string;
  attributes: {
    price: string;
    allocation: string;
    claimbackPeriod: number;
    tgeNumerator: number;
    tgeDenominator: number;
    cliffDuration: number;
    cliffNumerator: number;
    cliffDenominator: number;
    vestingPeriodCount: number;
    vestingPeriodDuration: number;
    tradeable: number;
  };
  proof: string[];
  leaf: string;
}

interface PresaleInfo {
  roundId: number;
  whitelistRoot: string;
  startTime: number;
  endTime: number;
  tokenAAddress: string;
  tokenBAddress: string;
}

/**
 * Whitelist API client for frontend applications
 */
class WhitelistAPIClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  /**
   * Check if user is whitelisted and get their data
   */
  async getUserWhitelistData(address: string, roundId: number): Promise<UserWhitelistData | null> {
    try {
      const response = await fetch(`${this.baseUrl}/whitelist/${roundId}/${address}`);
      
      if (response.status === 404) {
        return null; // User not whitelisted
      }
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch whitelist data:', error);
      throw error;
    }
  }

  /**
   * Get presale information
   */
  async getPresaleInfo(roundId: number): Promise<PresaleInfo> {
    const response = await fetch(`${this.baseUrl}/presale/${roundId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch presale info: ${response.status}`);
    }
    
    return await response.json();
  }
}

/**
 * VestPresale contract interaction helper
 */
class VestPresaleClient {
  private contract: ethers.Contract;
  private provider: ethers.providers.Provider;

  constructor(contractAddress: string, provider: ethers.providers.Provider) {
    this.provider = provider;
    
    // VestPresale ABI (essential functions only)
    const abi = [
      "function buy(uint256 roundId, uint256 amountA, tuple(uint256 price, uint256 allocation, uint256 claimbackPeriod, uint32 tgeNumerator, uint32 tgeDenominator, uint32 cliffDuration, uint32 cliffNumerator, uint32 cliffDenominator, uint32 vestingPeriodCount, uint32 vestingPeriodDuration, uint8 tradeable) attributes, bytes32[] proof) payable returns (uint256)",
      "function rounds(uint256) view returns (string name, uint256 startTimestamp, uint256 endTimestamp, uint256 price, uint256 totalTokenA, uint256 totalTokenB, uint256 totalSold, bytes32 whitelistRoot, string proofsUri, uint8 state)",
      "function tokenA() view returns (address)",
      "function tokenB() view returns (address)"
    ];
    
    this.contract = new ethers.Contract(contractAddress, abi, provider);
  }

  /**
   * Get round information
   */
  async getRoundInfo(roundId: number) {
    const roundData = await this.contract.rounds(roundId);
    return {
      name: roundData.name,
      startTimestamp: roundData.startTimestamp.toNumber(),
      endTimestamp: roundData.endTimestamp.toNumber(),
      price: roundData.price.toString(),
      totalTokenA: roundData.totalTokenA.toString(),
      totalTokenB: roundData.totalTokenB.toString(),
      totalSold: roundData.totalSold.toString(),
      whitelistRoot: roundData.whitelistRoot,
      proofsUri: roundData.proofsUri,
      state: roundData.state
    };
  }

  /**
   * Calculate required ETH for purchase
   */
  calculateRequiredETH(amountA: string, pricePerToken: string): string {
    const amount = ethers.BigNumber.from(amountA);
    const price = ethers.BigNumber.from(pricePerToken);
    return amount.mul(price).div(ethers.utils.parseEther("1")).toString();
  }

  /**
   * Execute buy transaction
   */
  async buy(
    signer: ethers.Signer,
    roundId: number,
    amountA: string,
    whitelistData: UserWhitelistData
  ): Promise<ethers.ContractTransaction> {
    const contractWithSigner = this.contract.connect(signer);
    
    // Calculate required ETH
    const requiredETH = this.calculateRequiredETH(amountA, whitelistData.attributes.price);
    
    // Prepare attributes tuple
    const attributes = {
      price: whitelistData.attributes.price,
      allocation: whitelistData.attributes.allocation,
      claimbackPeriod: whitelistData.attributes.claimbackPeriod,
      tgeNumerator: whitelistData.attributes.tgeNumerator,
      tgeDenominator: whitelistData.attributes.tgeDenominator,
      cliffDuration: whitelistData.attributes.cliffDuration,
      cliffNumerator: whitelistData.attributes.cliffNumerator,
      cliffDenominator: whitelistData.attributes.cliffDenominator,
      vestingPeriodCount: whitelistData.attributes.vestingPeriodCount,
      vestingPeriodDuration: whitelistData.attributes.vestingPeriodDuration,
      tradeable: whitelistData.attributes.tradeable
    };
    
    return await contractWithSigner.buy(
      roundId,
      amountA,
      attributes,
      whitelistData.proof,
      { value: requiredETH }
    );
  }
}

/**
 * Complete frontend integration example
 */
class PresaleApp {
  private whitelistAPI: WhitelistAPIClient;
  private presaleClient: VestPresaleClient;
  private provider: ethers.providers.Web3Provider;

  constructor(
    whitelistAPIUrl: string,
    presaleContractAddress: string,
    provider: ethers.providers.Web3Provider
  ) {
    this.whitelistAPI = new WhitelistAPIClient(whitelistAPIUrl);
    this.presaleClient = new VestPresaleClient(presaleContractAddress, provider);
    this.provider = provider;
  }

  /**
   * Check if user can participate in presale
   */
  async checkUserEligibility(userAddress: string, roundId: number) {
    try {
      // Get whitelist data
      const whitelistData = await this.whitelistAPI.getUserWhitelistData(userAddress, roundId);
      
      if (!whitelistData) {
        return {
          eligible: false,
          reason: "Address not whitelisted for this round"
        };
      }

      // Get round info
      const roundInfo = await this.presaleClient.getRoundInfo(roundId);
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if round is active
      if (currentTime < roundInfo.startTimestamp) {
        return {
          eligible: false,
          reason: "Presale has not started yet",
          startTime: roundInfo.startTimestamp
        };
      }

      if (currentTime > roundInfo.endTimestamp) {
        return {
          eligible: false,
          reason: "Presale has ended",
          endTime: roundInfo.endTimestamp
        };
      }

      if (roundInfo.state !== 1) { // Assuming 1 = SALE state
        return {
          eligible: false,
          reason: "Round is not in sale state"
        };
      }

      return {
        eligible: true,
        whitelistData,
        roundInfo,
        maxAllocation: ethers.utils.formatEther(whitelistData.attributes.allocation),
        pricePerToken: ethers.utils.formatEther(whitelistData.attributes.price),
        vestingInfo: {
          tgePercent: (whitelistData.attributes.tgeNumerator / whitelistData.attributes.tgeDenominator) * 100,
          cliffDuration: whitelistData.attributes.cliffDuration,
          vestingPeriods: whitelistData.attributes.vestingPeriodCount,
          periodDuration: whitelistData.attributes.vestingPeriodDuration
        }
      };
    } catch (error) {
      console.error('Error checking eligibility:', error);
      return {
        eligible: false,
        reason: "Error checking eligibility",
        error
      };
    }
  }

  /**
   * Execute purchase
   */
  async purchaseTokens(roundId: number, amountInTokens: string) {
    try {
      // Get user address
      const signer = this.provider.getSigner();
      const userAddress = await signer.getAddress();

      // Check eligibility
      const eligibility = await this.checkUserEligibility(userAddress, roundId);
      
      if (!eligibility.eligible) {
        throw new Error(eligibility.reason);
      }

      const { whitelistData } = eligibility;
      
      // Validate amount
      const requestedAmount = ethers.utils.parseEther(amountInTokens);
      const maxAllocation = ethers.BigNumber.from(whitelistData!.attributes.allocation);
      
      if (requestedAmount.gt(maxAllocation)) {
        throw new Error(`Requested amount exceeds allocation limit of ${ethers.utils.formatEther(maxAllocation)} tokens`);
      }

      // Calculate cost
      const cost = this.presaleClient.calculateRequiredETH(
        requestedAmount.toString(),
        whitelistData!.attributes.price
      );

      console.log(`Purchasing ${amountInTokens} tokens for ${ethers.utils.formatEther(cost)} ETH`);

      // Execute transaction
      const tx = await this.presaleClient.buy(
        signer,
        roundId,
        requestedAmount.toString(),
        whitelistData!
      );

      console.log(`Transaction sent: ${tx.hash}`);
      
      // Wait for confirmation
      const receipt = await tx.wait();
      console.log(`Transaction confirmed in block ${receipt.blockNumber}`);

      return {
        success: true,
        transactionHash: tx.hash,
        blockNumber: receipt.blockNumber,
        amountPurchased: amountInTokens,
        ethSpent: ethers.utils.formatEther(cost)
      };

    } catch (error) {
      console.error('Purchase failed:', error);
      return {
        success: false,
        error: error.message || 'Unknown error occurred'
      };
    }
  }
}

// Example usage in a React component or vanilla JS
export async function exampleUsage() {
  // Initialize with your configuration
  const provider = new ethers.providers.Web3Provider(window.ethereum);
  const app = new PresaleApp(
    "https://api.yourproject.com", // Your whitelist API
    "0x...", // VestPresale contract address
    provider
  );

  // Connect wallet
  await provider.send("eth_requestAccounts", []);
  const userAddress = await provider.getSigner().getAddress();

  // Check eligibility for round 1
  const eligibility = await app.checkUserEligibility(userAddress, 1);
  
  if (eligibility.eligible) {
    console.log("✅ User is eligible!");
    console.log(`Max allocation: ${eligibility.maxAllocation} tokens`);
    console.log(`Price: ${eligibility.pricePerToken} ETH per token`);
    
    // Purchase 100 tokens
    const result = await app.purchaseTokens(1, "100");
    
    if (result.success) {
      console.log("🎉 Purchase successful!");
      console.log(`Transaction: ${result.transactionHash}`);
    } else {
      console.error("❌ Purchase failed:", result.error);
    }
  } else {
    console.log("❌ User not eligible:", eligibility.reason);
  }
}

// Export classes for use in applications
export { WhitelistAPIClient, VestPresaleClient, PresaleApp };
