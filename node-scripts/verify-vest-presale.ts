import { $, resolveSync } from 'bun';
import { parseArgs } from 'util';
import foundry from '../foundry.toml';

function replaceEmptyStringWithQuotes(input: string): string {
  // Replace ', ,' with ', "",'
  return input.replace(/,\s*,/g, ', "",');
}

const {
  values: { rpcUrl },
} = parseArgs({
  args: Bun.argv,
  options: {
    rpcUrl: {
      type: 'string',
    },
  },
  strict: true,
  allowPositionals: true,
});
if (!rpcUrl) throw new Error('rpcUrl is required');

if (rpcUrl !== 'anvil') {
  const rpcEndpoint = foundry.rpc_endpoints[rpcUrl];
  if (!rpcEndpoint) throw new Error(`rpcUrl ${rpcUrl} not found in foundry.toml`);

  /**
   * VestController createPresale
   *
   * tuple presaleConfig,
   * tuple[] rounds,
   * tuple membershipConfig
   *
   * ((uint16,uint16,uint16,uint16),address,address,address,address,uint256,uint256,uint256),
   * (string,uint256,uint256,bytes32,string,bytes)[],
   * ((uint16,uint16),(address,string,string),address)
   */

  // https://book.getfoundry.sh/reference/cast/cast-chain-id
  const chainId = (await $`cast chain-id --rpc-url ${rpcEndpoint}`.text()).replace(/\n$/, '');
  if (!chainId) throw new Error(`Can not find chainId for rpcUrl ${rpcUrl}`);

  const path = resolveSync(`./broadcast/Presale.deploy.s.sol/${chainId}/run-latest.json`, process.cwd());

  const file = Bun.file(path);
  const contents = await file.json();

  const createPresaleTransaction = contents.transactions.find(
    transaction => transaction.contractName === 'VestController' && transaction.function?.startsWith('createPresale')
  );

  const [presaleConfiguration, _rounds] = createPresaleTransaction.arguments;
  const rounds = replaceEmptyStringWithQuotes(_rounds);

  const createPresaleReceipt = contents.receipts.find(receipt => receipt.transactionHash === createPresaleTransaction.hash);

  const vestMembershipAddress = createPresaleReceipt.logs[0].address;
  const vestPresaleAddress = createPresaleReceipt.logs[1].address;
  const feeCollectorProviderAddress = createPresaleReceipt.to;

  console.log(`Starting verification of VestPresale smart contract:`);
  console.log({ chainId, vestPresaleAddress, rpcUrl, rpcEndpoint });
  console.log({ vestMembershipAddress, feeCollectorProviderAddress, presaleConfiguration, rounds });

  /**
   * VestPresale constructor
   *
   * IVestMembership membership_,
   * IVestFeeCollectorProvider feeCollectorProvider_,
   * Presale.Configuration memory configuration,
   * Round[] memory rounds_
   *
   * address,
   * address,
   * ((uint16,uint16,uint16,uint16),address,address,address,address,uint256,uint256,uint256),
   * (string,uint256,uint256,bytes32,string,bytes)[]
   */

  const vestPresaleConstructorSignature =
    'constructor(address,address,((uint16,uint16,uint16,uint16),address,address,address,address,uint256,uint256,uint256),(string,uint256,uint256,bytes32,string,bytes)[])';

  // --etherscan-api-key=$ARBISCAN_API_KEY
  await $`forge verify-contract --etherscan-api-key=********************************** --rpc-url ${rpcUrl} ${vestPresaleAddress} src/VestPresale.sol:VestPresale --constructor-args $(cast abi-encode ${vestPresaleConstructorSignature} ${vestMembershipAddress} ${feeCollectorProviderAddress} ${presaleConfiguration} ${rounds})`;
}
