// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

/**
 * @title Bonus
 * @notice A struct to represent bonus configuration with numerator and denominator.
 * @dev Used to define allowed bonus values for presale rounds.
 */
struct Bonus {
    /// @notice Numerator of the bonus fraction (e.g., 15 for 15% bonus)
    uint32 numerator;
    /// @notice Denominator of the bonus fraction (e.g., 100 for percentage-based bonus)
    uint32 denominator;
}
