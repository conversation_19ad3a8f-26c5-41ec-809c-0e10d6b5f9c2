import { program } from "@commander-js/extra-typings"
import fs from "fs-extra"
import path from "path";
import { readCsv, validateHeaders } from "./utils/csv";
import { computeTree } from "./utils/merkle-tree";


const whitelistHeaders = [
  "address",
  "tier",
  "price",
  "allocation",
  "claimbackPeriod",
  "tgeNumerator",
  "tgeDenominator",
  "cliffDuration",
  "cliffNumerator",
  "cliffDenominator",
  "vestingPeriodCount",
  "vestingPeriodDuration",
  "tradeable",
];

export interface WhitelistRow {
  address: string
  tokenId: number
  key: number
  tier: number
  price: string
  allocation: string
  claimbackPeriod: string
  tgeNumerator: number
  tgeDenominator: number
  cliffDuration: number
  cliffNumerator: number
  cliffDenominator: number
  vestingPeriodCount: number
  vestingPeriodDuration: number
  tradeable: 1 | 2
}

program
  .command("generate")
  .argument("<project-name>", "Name of the project for which you want take snapshot")
  .action(async (projectName) => {
    const absoluteWhitelistPath = path.resolve(`data/${projectName}/whitelist.csv`)
    if (!(await fs.exists(absoluteWhitelistPath)))
      throw new Error(`Whitelist file file at ${absoluteWhitelistPath} seems to not exist`)

    const whitelist = await readCsv<WhitelistRow>(absoluteWhitelistPath);
    // validateHeaders(absoluteWhitelistPath, Object.keys(whitelist[0]), whitelistHeaders);

    // create new const without tier property
    const whitelistWithoutTier = whitelist.map(({ tier, ...rest }) => rest);

    const tree = computeTree(whitelistWithoutTier);

    fs.writeFileSync(`./data/${projectName}/root.txt`, tree.root);

    // create proofs directory if it doesn't exist
    if (!(await fs.exists(`./data/${projectName}/proofs`)))
      await fs.mkdir(`./data/${projectName}/proofs`);

    // for each row in tree.proofs write the proof in separate file named after the address to lower case. In the directory coresponding to tier
    // for (const [index, row] of whitelist.entries()) {
    //   const address = row.address.toLowerCase();
    //   const tier = row.tier;
    //   const tierDir = `./data/${projectName}/proofs/${tier}`;
    //   if (!(await fs.exists(tierDir))) await fs.mkdir(tierDir);
    //   fs.writeFileSync(`${tierDir}/${address}.json`, JSON.stringify(tree.proofs[index], null, 2));
    // }

    // for each row in tree.proofs write the proof in one file named "proofs.json". Set key as row.key
    const proofs: {[key: number]: any} = {};
    for (const [index, row] of whitelist.entries()) {
      // const key = row.key
      const key = row.tokenId

      proofs[key]  = tree.proofs[index].proofs
      
    }
    const tierDir = `./data/${projectName}/proofs/auctions`;
    if (!(await fs.exists(tierDir))) await fs.mkdir(tierDir);
    fs.writeFileSync(`${tierDir}/proofs.json`, JSON.stringify(proofs, null, 2));

  })
  ; (async () => program.parseAsync())()
