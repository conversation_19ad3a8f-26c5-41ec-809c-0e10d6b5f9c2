import { program } from "@commander-js/extra-typings"
import fs from "fs-extra"
import path from "path";
import { readCsv, validateHeaders } from "./utils/csv";
import { computeTree } from "./utils/merkle-tree";
import { uploadToS3 } from "./utils/s3";
import dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();

const whitelistHeaders = [
  "address",
  "tier",
  "price",
  "allocation",
  "claimbackPeriod",
  "tgeNumerator",
  "tgeDenominator",
  "cliffDuration",
  "cliffNumerator",
  "cliffDenominator",
  "vestingPeriodCount",
  "vestingPeriodDuration",
  "tradeable",
];

export interface WhitelistRow {
  address: string
  tier: number
  price: string
  allocation: string
  claimbackPeriod: string
  tgeNumerator: number
  tgeDenominator: number
  cliffDuration: number
  cliffNumerator: number
  cliffDenominator: number
  vestingPeriodCount: number
  vestingPeriodDuration: number
  tradeable: 1 | 2
}

program
  .command("generate")
  .argument("<project-name>", "Name of the project for which you want take snapshot")
  .action(async (projectName) => {
    const absoluteWhitelistPath = path.resolve(`data/${projectName}/whitelist.csv`)
    if (!(await fs.exists(absoluteWhitelistPath)))
      throw new Error(`Whitelist file file at ${absoluteWhitelistPath} seems to not exist`)

    const whitelist = await readCsv<WhitelistRow>(absoluteWhitelistPath);
    validateHeaders(absoluteWhitelistPath, Object.keys(whitelist[0]), whitelistHeaders);

    // create new const without tier property
    const whitelistWithoutTier = whitelist.map(({ tier, ...rest }) => rest);

    const tree = computeTree(whitelistWithoutTier);

    fs.writeFileSync(`./data/${projectName}/root.txt`, tree.root);

    // create proofs directory if it doesn't exist
    if (!(await fs.exists(`./data/${projectName}/proofs`)))
      await fs.mkdir(`./data/${projectName}/proofs`);

    // for each row in tree.proofs write the proof in separate file named after the address to lower case. In the directory coresponding to tier
    for (const [index, row] of whitelist.entries()) {
      const address = row.address.toLowerCase();
      const tier = row.tier;
      const tierDir = `./data/${projectName}/proofs/${tier}`;
      if (!(await fs.exists(tierDir))) await fs.mkdir(tierDir);
      fs.writeFileSync(`${tierDir}/${address}.json`, JSON.stringify(tree.proofs[index], null, 2));
    }

  })

program
  .command("upload")
  .description("Upload generated proofs to an S3 bucket")
  .argument("<project-name>", "Name of the project for which you want to upload proofs")
  .option("-b, --bucket <bucket>", "S3 bucket name", process.env.AWS_S3_BUCKET)
  .option("-r, --region <region>", "AWS region", process.env.AWS_REGION || "ap-southeast-1")
  .option("-n, --name <n>", "Custom folder name in S3 (defaults to project-name)")
  .option("--roundId <roundId>", "Round ID for the proofs", "1")
  .action(async (projectName, options) => {
    const proofsPath = path.resolve(`data/${projectName}/proofs`);
    
    if (!(await fs.exists(proofsPath))) {
      throw new Error(`Proofs directory at ${proofsPath} does not exist. Generate proofs first.`);
    }
    
    // Check if bucket is provided either via command line or .env
    if (!options.bucket) {
      throw new Error("S3 bucket name is required. Provide it using --bucket option or set AWS_S3_BUCKET in your .env file.");
    }
    
    // Use custom folder name if provided, otherwise use project name
    const folderName = options.name || projectName;
    
    console.log(`Uploading proofs for project ${projectName} to S3 bucket ${options.bucket} under folder ${folderName}/proofs/${options.roundId}...`);
    
    // Create the S3 key prefix using the folder name and roundId
    const s3KeyPrefix = `${folderName}/proofs/${options.roundId}`;
    
    try {
      await uploadToS3(proofsPath, options.bucket, s3KeyPrefix, options.region);
      
      console.log(`Successfully uploaded proofs for project ${projectName} to S3 bucket ${options.bucket} under folder ${folderName}/proofs/${options.roundId}`);
    } catch (error) {
      console.error(`Failed to upload proofs to S3:`, error);
      process.exit(1);
    }
  })
  ; (async () => program.parseAsync())()
