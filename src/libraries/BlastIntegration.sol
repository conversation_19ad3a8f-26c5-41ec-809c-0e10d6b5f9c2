// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IBlast } from "../libraries/IBlast.sol";
import { IERC20Rebasing, YieldMode } from "../libraries/IERC20Rebasing.sol";

abstract contract BlastIntegration {
    IBlast public constant BLAST = IBlast(******************************************);
    IERC20Rebasing public constant USDB = IERC20Rebasing(******************************************);
    IERC20Rebasing public constant WETH = IERC20Rebasing(******************************************);

    modifier protectedClaim() virtual;

    error UnauthorizedClaim();

    constructor(address governor) {
        if (address(BLAST).code.length == 0) return;
        BLAST.configureClaimableYield();
        BLAST.configureClaimableGas();
        BLAST.configureGovernor(governor);
        USDB.configure(YieldMode.CLAIMABLE);
        WETH.configure(YieldMode.CLAIMABLE);
    }

    function claim(address recipient) external protectedClaim {
        USDB.claim(recipient, USDB.getClaimableAmount(address(this)));
        WETH.claim(recipient, WETH.getClaimableAmount(address(this)));
    }
}
