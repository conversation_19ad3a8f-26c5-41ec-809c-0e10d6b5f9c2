import <PERSON> from "papapar<PERSON>";
import fs from "fs";
import path from "path";

export const writeToCsv = (data: any, writeTo: string): void => {
  const dir = path.dirname(writeTo);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const csvData = Papa.unparse(data, {
    header: true,
  });

  // Zapisz dane do pliku
  fs.writeFileSync(writeTo, csvData);
}

export const readCsv = <T>(path: string): Promise<T[]> => {
  return new Promise((resolve, reject) => {
    fs.readFile(path, 'utf8', (error, csvData) => {
      if (error) {
        console.error('Error reading CSV file:', error.message);
        reject(new Error('Error reading CSV file'));
        return;
      }

      const csvOptions: Papa.ParseConfig = {
        header: true,
        dynamicTyping: true,
      };

      // Parse data from CSV file
      Papa.parse(csvData, {
        ...csvOptions,
        complete: (result) => {
          // Transform data into an array of objects
          const dataArray: T[] = result.data;
          resolve(dataArray);
        },
        error: (parseError: any) => {
          console.error('Error parsing CSV file:', parseError.message);
          reject(new Error('Error parsing CSV file'));
        },
      });
    });
  });
};

export const validateHeaders = (filePath: string, headers: string[], expectedHeaders: string[]): void => {
  if (headers.length === 0) {
    throw new Error(`Allocation file file at ${filePath} is empty`)
  }

  if (headers.length !== expectedHeaders.length) {
    throw new Error(`Allocation file file at ${filePath} has wrong number of columns`)
  }

  // check if all required fields are present
  for (const header of expectedHeaders) {
    if (!headers.includes(header)) {
      throw new Error(`Allocation file file at ${filePath} is missing column ${header}`)
    }
  }
}