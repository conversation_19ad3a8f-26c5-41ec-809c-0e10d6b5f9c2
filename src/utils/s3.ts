import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import fs from "fs-extra";
import path from "path";
import dotenv from "dotenv";
import { createHash } from "crypto";

// Load environment variables from .env file
dotenv.config();

// Maximum number of concurrent uploads
const MAX_CONCURRENT_UPLOADS = 20;

/**
 * Uploads a file or directory to an S3 bucket
 * @param sourcePath Local path to file or directory to upload
 * @param bucketName S3 bucket name
 * @param s3KeyPrefix Key prefix in S3 (like a folder path)
 * @param region AWS region
 */
export async function uploadToS3(
  sourcePath: string,
  bucketName: string,
  s3KeyPrefix: string,
  region: string = "eu-west-1"
): Promise<void> {
  // Create S3 client with credentials from environment variables
  const s3Client = new S3Client({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    },
  });
  
  // Check if credentials are provided
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    throw new Error("AWS credentials not found. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your .env file.");
  }
  
  // Check if source path exists
  if (!(await fs.exists(sourcePath))) {
    throw new Error(`Source path ${sourcePath} does not exist`);
  }
  
  // Check if source is a directory
  const stats = await fs.stat(sourcePath);
  if (stats.isDirectory()) {
    await uploadDirectoryToS3Parallel(sourcePath, bucketName, s3KeyPrefix, s3Client);
  } else {
    await uploadFileToS3(sourcePath, bucketName, s3KeyPrefix, s3Client);
  }
  
  console.log(`\nSuccessfully uploaded ${sourcePath} to s3://${bucketName}/${s3KeyPrefix}`);
}

/**
 * Uploads a single file to S3
 */
async function uploadFileToS3(
  filePath: string,
  bucketName: string,
  s3KeyPrefix: string,
  s3Client: S3Client
): Promise<void> {
  const fileStream = fs.createReadStream(filePath);
  const fileName = path.basename(filePath);
  const s3Key = s3KeyPrefix ? `${s3KeyPrefix}/${fileName}` : fileName;
  
  const upload = new Upload({
    client: s3Client,
    params: {
      Bucket: bucketName,
      Key: s3Key,
      Body: fileStream,
      ContentType: getContentType(filePath),
    },
  });
  
  await upload.done();
  console.log(`Uploaded ${filePath} to s3://${bucketName}/${s3Key}`);
}

/**
 * Recursively uploads a directory to S3 with parallel processing
 */
async function uploadDirectoryToS3Parallel(
  dirPath: string,
  bucketName: string,
  s3KeyPrefix: string,
  s3Client: S3Client
): Promise<void> {
  // Get all files in the directory and subdirectories
  const allFiles = await getAllFiles(dirPath);
  
  console.log(`Found ${allFiles.length} files to upload in ${dirPath}`);
  
  // Create a pool of promises with limited concurrency
  const uploadPromises: Promise<void>[] = [];
  const inProgress = new Set<Promise<void>>();
  let completed = 0;
  
  // Update progress function
  const updateProgress = () => {
    completed++;
    const percentage = Math.round((completed / allFiles.length) * 100);
    process.stdout.write(`\rUploading... ${completed}/${allFiles.length} files (${percentage}%)`);
  };
  
  // Process files in batches with limited concurrency
  for (const file of allFiles) {
    // Calculate the S3 key based on the relative path from the source directory
    const relativePath = path.relative(dirPath, file);
    const s3Key = s3KeyPrefix ? `${s3KeyPrefix}/${relativePath.replace(/\\/g, '/')}` : relativePath.replace(/\\/g, '/');
    
    // Create the upload promise
    const uploadPromise = uploadSingleFile(file, bucketName, s3Key, s3Client).then(() => {
      inProgress.delete(uploadPromise);
      updateProgress();
    });
    
    uploadPromises.push(uploadPromise);
    inProgress.add(uploadPromise);
    
    // Wait if we've reached the maximum concurrent uploads
    if (inProgress.size >= MAX_CONCURRENT_UPLOADS) {
      await Promise.race(inProgress);
    }
  }
  
  // Wait for all remaining uploads to complete
  await Promise.all(uploadPromises);
}

/**
 * Uploads a single file to S3 using PutObjectCommand for better performance with small files
 */
async function uploadSingleFile(
  filePath: string,
  bucketName: string,
  s3Key: string,
  s3Client: S3Client
): Promise<void> {
  try {
    const fileContent = await fs.readFile(filePath);
    
    // Calculate MD5 hash for Content-MD5 header
    const md5Hash = createHash('md5').update(fileContent).digest('base64');
    
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: s3Key,
      Body: fileContent,
      ContentType: getContentType(filePath),
      ContentMD5: md5Hash,
    });
    
    await s3Client.send(command);
  } catch (error) {
    console.error(`Error uploading ${filePath}: ${error}`);
    throw error;
  }
}

/**
 * Recursively gets all files in a directory and its subdirectories
 */
async function getAllFiles(dirPath: string): Promise<string[]> {
  const files: string[] = [];
  
  async function traverseDirectory(currentPath: string) {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);
      
      if (entry.isDirectory()) {
        await traverseDirectory(fullPath);
      } else {
        files.push(fullPath);
      }
    }
  }
  
  await traverseDirectory(dirPath);
  return files;
}

/**
 * Determines the content type based on file extension
 */
function getContentType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  
  switch (ext) {
    case '.json':
      return 'application/json';
    case '.txt':
      return 'text/plain';
    case '.csv':
      return 'text/csv';
    default:
      return 'application/octet-stream';
  }
}
