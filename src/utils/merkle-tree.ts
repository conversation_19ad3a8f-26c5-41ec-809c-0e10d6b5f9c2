import { isAddress, AbiCoder } from 'ethers';
import keccak256 from 'keccak256';
import MerkleTree from 'merkletreejs';
import { WhitelistRow } from "..";

export function getTypes(rowData: string[]) {
  const types: string[] = [];

  for (const cell of rowData) {
    if (cell.match(/^[0-9]+$/)) {
      types.push('uint256');
      continue;
    }

    if (isAddress(cell)) {
      types.push('address');
      continue;
    }

    types.push('string');
  }

  return types;
}

export function getRecords(data: { [key: string]: any }[]): string[][] {
  return data.map((row) => {
    const record: string[] = [];
    for (const value of Object.values(row)) {
      record.push(value.toString());
    }

    return record;
  });
}

// TreeData is WhitelistRow but without tier. Use Ommited<T, 'tier'> instead of WhitelistRow once TS 4.1 is released
interface TreeData extends Omit<WhitelistRow, 'tier'> { }

export function computeTree(data: TreeData[]) {
  const records = getRecords(data);
  const types = getTypes(records[0]);
  const abiCoder = new AbiCoder();

  // For each record, encode it and hash it
  const leafs = records.map((record) => keccak256(abiCoder.encode(types, record)));

  // generate merkle tree from leafs
  const tree = new MerkleTree(leafs, keccak256, { sort: true });

  const proofs = data.map((row, index) => {
    const leaf = tree.getHexProof(leafs[index]);
    return { ...row, proofs: leaf };
  })

  // Verify each proof
  for (const [index, row] of proofs.entries()) {
    const _proof = row.proofs;
    // const _records = row without proofs key
    const _records: any = []
    for (const [key, value] of Object.entries(row)) {
      if (key !== 'proofs') {
        _records.push(value)
      }
    }
    const _leaf = keccak256(abiCoder.encode(types, _records))

    const verified = tree.verify(_proof, _leaf, tree.getHexRoot())

    if (!verified) {
      console.log({ ...row, verified })
    }
  }

  return { root: tree.getHexRoot(), proofs };
}
